import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';

extension StringExtensions on String {
  String get translateErrorMessage {
    try {
      //{"provider_id":["The provider id field is required."],"date":["The date field must be an array."],"time":["The time field must be an array."],"duration":["The duration field is required."]}
      final allErrorsLines = this.split(':');

      final errors = <String>[];

      for (var i = 0; i < allErrorsLines.length; i++) {
        final errorLine = allErrorsLines[i];
        if (errorLine.contains('[')) {
          final error = errorLine.split('[')[1].split(']')[0];
          errors.add(error);
        }
      }


      return errors.join('\n');

    } catch (e) {
      return this;
    }
}
}
