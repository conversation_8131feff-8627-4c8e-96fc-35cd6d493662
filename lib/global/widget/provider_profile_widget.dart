import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_profile/controllers/my_profile_controller.dart';

import '../constants/constants.dart';
import '../controllers/language_controller.dart';
import 'area_and_city_profile_widget.dart';
import 'custom_button.dart';
import 'custom_form_field.dart';
import 'custom_multi_drop_down_form_field.dart';

class ProviderProfileWidget extends StatelessWidget {
  const ProviderProfileWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(MyProfileController());
    return GetBuilder<MyProfileController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFormField(
              keyboardType: TextInputType.text,
              label: Get.find<LanguageController>().keys.value.providerName!,
              controller: controller.providerName,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderName!;
                }
                return null;
              },
            ),

            const AreaAndCityProfileWidget(),

            //! Work Zones
            // BaseSearchDropDown(
            //   data: controller.shownWorkZones
            //       .map(
            //         (zone) => DropDownValueModel(
            //           name: zone.name ?? '',
            //           value: zone,
            //         ),
            //       )
            //       .toList(),
            //   onChanged: controller.onChangeWorkZones,
            //   selectedValue:
            //       controller.initialWorkZones.map((e) => e.name).toList()[0],
            //   label: Get.find<LanguageController>().keys.value.workAreas!,
            //   // customButton: TextButton(
            //   //   onPressed: controller.onAddAreaPressed,
            //   //   child: Text(Get.find<LanguageController>().keys.value.add!),
            //   // ),
            // ),
            CustomMultiDropDownFormField(
              dataList: controller.shownWorkZones
                  .map(
                    (zone) => DropDownValueModel(
                      name: zone.name ?? '',
                      value: zone,
                    ),
                  )
                  .toList(),
              onChanged: controller.onChangeWorkZones,
              initialValue:
                  controller.initialWorkZones.map((e) => e.name).toList(),
              label: Get.find<LanguageController>().keys.value.workAreas!,
              customButton: TextButton(
                onPressed: controller.onAddAreaPressed,
                child: Text(Get.find<LanguageController>().keys.value.add!),
              ),
            ),

            CustomMultiDropDownFormField(
              label: Get.find<LanguageController>().keys.value.skills!,
              validator: (value) {
                if (value.isEmpty) {
                  return "Required";
                }
                return null;
              },
              dataList: controller.shownSkills
                  .map((skill) =>
                      DropDownValueModel(name: skill.name!, value: skill))
                  .toList(),
              onChanged: controller.onChangeSkills,
              initialValue:
                  controller.initialSkills.map((e) => e.name).toList().isEmpty
                      ? controller.choosedSkills.map((e) => e.name).toList()
                      : controller.initialSkills.map((e) => e.name).toList(),
            ),

            //! Provider Phone
            CustomFormField(
              keyboardType: TextInputType.phone,
              label: Get.find<LanguageController>().keys.value.providerNumber!,
              controller: controller.providerPhone,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderPhoneNumber!;
                }
                return null;
              },
            ),
            CustomFormField(
              keyboardType: TextInputType.text,
              label: Get.find<LanguageController>().keys.value.providerId!,
              controller: controller.providerIdNumber,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderIdNumber!;
                }
                return null;
              },
            ),
            // CustomFormField(
            //   enabled: false,
            //   label: Get.find<LanguageController>().keys.value.idFile!,
            //   controller: controller.providerImageController,
            //   validator: (value) {
            //     if (value.isEmpty) {
            //       return Get.find<LanguageController>().keys.value.pleaseEnterChooseProviderImage!;
            //     }
            //     return null;
            //   },
            // ),

            //? Suspend Switch
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 10.0, vertical: 5.0),
              child: Row(
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.beAsUser ??
                        "Be As User",
                    style: const TextStyle(
                      color: primaryColor,
                      fontSize: 16,
                    ),
                  ),
                  Switch(
                    value: controller.isSuspend.value,
                    onChanged: controller.onSuspendSwitchChanged,
                    activeColor: primaryColor,
                  ),
                ],
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.idFile!,
                    style: const TextStyle(
                      color: primaryColor,
                      fontSize: 16,
                    ),
                  ),
                  InkWell(
                    onTap: controller.onChangeProviderImage,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: primaryColor.withOpacity(0.6),
                        ),
                      ),
                      child: controller.providerImage.value.path.isEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Image.network(
                                controller.homeController.user.value.provider
                                        ?.idFile ??
                                    "",
                                width: Get.width,
                                height: 150.h,
                                fit: BoxFit.fill,
                                errorBuilder: (context, error, stackTrace) =>
                                    Row(
                                  children: [
                                    const Icon(
                                      Icons.add,
                                      size: 40,
                                    ),
                                    Expanded(
                                      child: Icon(
                                        Icons.image,
                                        color: primaryColor.withOpacity(0.6),
                                        size: Get.width * 0.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Image.file(
                                controller.providerImage.value,
                                width: Get.width,
                                height: 150.h,
                                fit: BoxFit.fill,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.save!,
                  onTap: () => controller.editData(context),
                  height: 42.h,
                  width: 135.w,
                ),
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.editSchduel!,
                  onTap: Get.find<MyProfileController>().onEditSchduelPressed,
                  height: 42.h,
                  width: 135.w,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
