import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/models/user_booking.dart';
import 'package:get_clean/global/widget/main_cached_image.dart';
import 'package:get_clean/global/widget/small_button.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../constants/theme.dart';
import '../controllers/language_controller.dart';

class OrderWidget extends StatelessWidget {
  final BookingData bookingData;
  final bool isUser;
  final bool showPhone;
  final bool isOffer;
  final bool isWaitingOffer;

  final bool backBeforeNavigate; //? Use for group orders in order details

  const OrderWidget({
    super.key,
    required this.isUser,
    required this.bookingData,
    this.showPhone = false,
    this.isOffer = false,
    this.isWaitingOffer = false,
    this.backBeforeNavigate = false,
  });

  @override
  Widget build(BuildContext context) {
    final isAccepted = bookingData.isConfirmed!;

    Widget mapIcon() {
      if (isUser || !isAccepted) return const SizedBox.shrink();

      return GestureDetector(
          onTap: () {
            final lat = bookingData.userLat;
            final lng = bookingData.userLong;

            if (lat == null || lng == null) {
              Get.snackbar(
                'Error',
                'User location not found',
                backgroundColor: Colors.red,
                colorText: Colors.white,
              );
              return;
            }

            launchUrlString('https://waze.com/ul?ll=$lat,$lng&navigate=yes',
                mode: LaunchMode.externalApplication);
          },
          child: const Icon(FontAwesomeIcons.waze));
    }

    return InkWell(
      onTap: () {
        if (backBeforeNavigate) {
          Get.back();
        }

        Get.toNamed(
          Routes.ORDER_DETAILS,
          arguments: {
            'order': bookingData.toJson(),
            'isOffer': isOffer,
            'isWaitingOffer': isWaitingOffer
          },
        );
      },
      child: Container(
        margin: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[400]!,
              blurRadius: 4,
            ),
          ],
        ),
        child: Row(
          children: [
            if (isUser)
              ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: MainCachedImage(
                  bookingData.provider?.image ??
                      "http://vishvish.matrixclouds.net/images/default-user.png",
                  fit: BoxFit.fill,
                  height: 78.h,
                  width: 84.w,
                ),
              ),
            if (!isUser)
              ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: MainCachedImage(
                  bookingData.user?.image ??
                      "http://vishvish.matrixclouds.net/images/default-user.png",
                  fit: BoxFit.fill,
                  height: 78.h,
                  width: 84.w,
                ),
              ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  if (!isUser)
                    Text(
                      bookingData.user?.name ?? '',
                      style: middleTextStyle,
                      overflow: TextOverflow.ellipsis,
                    ),
                  if (isUser)
                    Text(
                      bookingData.provider?.name ?? "Any Provider",
                      style: middleTextStyle,
                      overflow: TextOverflow.ellipsis,
                    ),
                  Text(
                    bookingData.address ?? "",
                    style: middleTextStyle,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (bookingData.deliver == 1)
                    Text(
                      '${Get.find<LanguageController>().keys.value.deliver}: ${bookingData.deliver == 1 ? Get.find<LanguageController>().keys.value.yes : Get.find<LanguageController>().keys.value.no} ',
                      style: regularTextStyle.copyWith(
                        color: Colors.red,
                      ),
                    ).paddingOnly(top: 5),
                  if (isAccepted)
                    InkWell(
                      onTap: () {
                        final phone = isUser
                            ? bookingData.provider!.phone!
                            : bookingData.user!.phone!;

                        launchUrlString('tel:$phone');
                      },
                      child: Row(
                        children: [
                          const Icon(
                            FontAwesomeIcons.phone,
                            size: 15,
                            color: primaryColor,
                          ),
                          SizedBox(width: 5.w),
                          Text(
                            bookingData.user?.phone ?? '',
                            style: middleTextStyle,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  '${bookingData.day ?? ''} ${bookingData.date ?? ''}',
                  style: smallGreyTextStyle,
                ),
                Text(
                  bookingData.startTime ?? '',
                  style: smallGreyTextStyle,
                ),
                Row(
                  children: [
                    SmallButton(
                        label:
                            Get.find<LanguageController>().keys.value.details!,
                        onTap: () {
                          if (backBeforeNavigate) {
                            Get.back();
                          }
                          Get.toNamed(
                            Routes.ORDER_DETAILS,
                            arguments: {
                              'order': bookingData.toJson(),
                              'isOffer': isOffer,
                              'isWaitingOffer': isWaitingOffer,
                            },
                          );
                        }),
                    if (bookingData.canChat!)
                      IconButton(
                        onPressed: () => Get.toNamed(
                          Routes.CHAT,
                          arguments: {
                            'order': bookingData.toJson(),
                          },
                        ),
                        icon: const Icon(FontAwesomeIcons.solidMessage),
                      ),
                    mapIcon(),
                    SizedBox(width: 20.w),
                  ],
                ),
              ],
            ),
            SizedBox(width: 5.w),
          ],
        ),
      ),
    );
  }
}
