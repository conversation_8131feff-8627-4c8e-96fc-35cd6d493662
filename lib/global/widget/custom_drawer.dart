import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/login/providers/local_provider.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_orders_controller.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/drawer_item.dart';

import '../controllers/global_values_controller.dart';
import '../controllers/language_controller.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.white,
        image: DecorationImage(
          image: AssetImage(
            'assets/images/drawer_background.png',
          ),
          fit: BoxFit.fill,
        ),
      ),
      width: Get.width * 0.7,
      height: Get.height,
      child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: 35,
                      backgroundImage: NetworkImage(
                        Get.find<HomeController>().user.value.image ?? '',
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        Get.find<HomeController>().user.value.name ?? '',
                        style: big2WhiteTextStyle,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 20),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.MY_PROFILE),
                  title: Get.find<LanguageController>().keys.value.myProfile!,
                  iconData: FontAwesomeIcons.solidCircleUser,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () {
                    Get.delete<MyBookingController>();
                    Get.put(MyBookingController());
                    // Get.find<MyOrdersController>().setIsBooking(true);
                    Get.toNamed(Routes.MY_BOOKING);
                  },
                  title: Get.find<LanguageController>().keys.value.myBooking!,
                  iconData: FontAwesomeIcons.solidCalendar,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () =>
                      Get.find<HomeController>().user.value.type! == 'user'
                          ? Get.toNamed(Routes.OFFER_SERVICE)
                          : Get.toNamed(Routes.PROVIDER_OFFER_SERVICE),
                  title: Get.find<LanguageController>()
                      .keys
                      .value
                      .myOfferServices!,
                  // title: "My Offer Services",
                  iconData: FontAwesomeIcons.bagShopping,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.FAVORITES),
                  title: Get.find<LanguageController>().keys.value.myFavorites!,
                  // title: "My Favorites",
                  iconData: FontAwesomeIcons.solidHeart,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.JOBS_VIEW),
                  title: Get.find<LanguageController>().keys.value.myJobs ?? 'My Jobs',
                  iconData: FontAwesomeIcons.briefcase,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.JOB_APPLICATIONS_VIEW),
                  title: Get.find<LanguageController>().keys.value.jobApplications ?? 'Job Applications',
                  iconData: FontAwesomeIcons.fileContract,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value &&
                  Get.find<HomeController>().user.value.type! != 'user')
                DrawerItem(
                  onTap: () {
                    // remove my orders controller
                    Get.delete<MyOrdersController>();

                    Get.put(MyOrdersController());
                    Get.toNamed(Routes.MY_ORDERS);
                  },
                  title: Get.find<LanguageController>().keys.value.myOrders!,
                  iconData: FontAwesomeIcons.tableList,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.NOTIFICATIONS),
                  title:
                      Get.find<LanguageController>().keys.value.notifications!,
                  iconData: FontAwesomeIcons.solidBell,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value &&
                  Get.find<HomeController>().user.value.type! == 'user')
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.USER_WALLET),
                  title: Get.find<LanguageController>().keys.value.wallet!,
                  iconData: FontAwesomeIcons.wallet,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value &&
                  Get.find<HomeController>().user.value.type! != 'user')
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.WALLET),
                  title: Get.find<LanguageController>().keys.value.wallet!,
                  iconData: FontAwesomeIcons.wallet,
                ),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.LANGUAGES),
                title: Get.find<LanguageController>().keys.value.language!,
                iconData: Icons.language,
              ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value &&
                  Get.find<HomeController>().user.value.type! != 'user')
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.PRICING),
                  title: Get.find<LanguageController>().keys.value.pricing!,
                  iconData: FontAwesomeIcons.dollarSign,
                ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value &&
                  Get.find<HomeController>().user.value.type! != 'user')
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.MY_OFFERS),
                  title: Get.find<LanguageController>().keys.value.myOffers!,
                  iconData: FontAwesomeIcons.ticket,
                ),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.OFFERS),
                title: Get.find<LanguageController>().keys.value.offers!,
                iconData: Icons.local_offer,
              ),
              Text(
                Get.find<LanguageController>().keys.value.support!,
                style: big2WhiteTextStyle,
              ),
              const SizedBox(height: 10),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.ABOUT_US),
                title: Get.find<LanguageController>().keys.value.aboutUs!,
                iconData: FontAwesomeIcons.circleExclamation,
              ),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.PRIVACY_POLICY),
                title: Get.find<LanguageController>().keys.value.privacyPolicy!,
                iconData: FontAwesomeIcons.lock,
              ),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.TERMS_AND_CONDITIONS),
                title: Get.find<LanguageController>()
                    .keys
                    .value
                    .termsAndConditions!,
                iconData: FontAwesomeIcons.solidFile,
              ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () => Get.toNamed(Routes.CONSULATION_REQUEST),
                  title: Get.find<LanguageController>()
                      .keys
                      .value
                      .colsultationRequest!,
                  iconData: FontAwesomeIcons.solidMessage,
                ),
              DrawerItem(
                onTap: () => Get.toNamed(Routes.FAQ),
                title: Get.find<LanguageController>().keys.value.faq!,
                iconData: FontAwesomeIcons.solidCircleQuestion,
              ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () async {
                    Get.find<GlobalValuesController>().setUserLoggedOut();
                    await LocalLoginProvider().deleteUserData();
                    Get.offAllNamed(Routes.LOGIN);
                  },
                  title: Get.find<LanguageController>().keys.value.logout!,
                  iconData: Icons.logout,
                ),
              if (!Get.find<GlobalValuesController>().isLoggedIn.value)
                DrawerItem(
                  onTap: () {
                    Get.offAllNamed(Routes.LOGIN);
                  },
                  title: Get.find<LanguageController>().keys.value.login!,
                  iconData: Icons.login,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
