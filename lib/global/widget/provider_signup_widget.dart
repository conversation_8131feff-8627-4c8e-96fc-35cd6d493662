import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';
import 'package:get_clean/global/widget/area_and_city_widget.dart';
import 'package:get_clean/global/widget/custom_multi_drop_down_form_field.dart';

import '../constants/constants.dart';
import '../controllers/language_controller.dart';
import 'custom_form_field.dart';

class ProviderSignupWidget extends StatelessWidget {
  const ProviderSignupWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFormField(
              keyboardType: TextInputType.text,
              label: Get.find<LanguageController>().keys.value.providerName!,
              controller: controller.providerName,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderName!;
                }
                return null;
              },
            ),
            const AreaAndCityWidget(),
            CustomFormField(
              keyboardType: TextInputType.text,
              label: Get.find<LanguageController>().keys.value.providerAddress!,
              controller: controller.providerAddress,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderAddress!;
                }
                return null;
              },
            ),
            CustomFormField(
              keyboardType: TextInputType.number,
              label: Get.find<LanguageController>().keys.value.providerNumber!,
              controller: controller.providerPhone,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderPhoneNumber!;
                }
                return null;
              },
            ),
            CustomFormField(
              keyboardType: TextInputType.number,
              label: Get.find<LanguageController>().keys.value.providerId!,
              controller: controller.providerIdNumber,
              validator: (value) {
                if (value.isEmpty) {
                  return Get.find<LanguageController>()
                      .keys
                      .value
                      .pleaseEnterProviderIdNumber!;
                }
                return null;
              },
            ),
            CustomMultiDropDownFormField(
              label: Get.find<LanguageController>().keys.value.skills!,
              validator: (value) {
                if (value.isEmpty) {
                  return "Required";
                }
                return null;
              },
              dataList: controller.shownSkills
                  .map((skill) =>
                      DropDownValueModel(name: skill.name!, value: skill))
                  .toList(),
              onChanged: controller.onChangeSkills,
            ),
            // CustomFormField(
            //   label: Get.find<LanguageController>().keys.value.idFile!,
            //   enabled: false,
            //   controller: controller.providerFilePath,
            //   validator: (value) {
            //     if (value.isEmpty) {
            //       return Get.find<LanguageController>().keys.value.pleaseEnterChooseProviderImage!;
            //     }
            //     return null;
            //   },
            // ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.idFile!,
                    style: const TextStyle(
                      color: primaryColor,
                      fontSize: 16,
                    ),
                  ),
                  InkWell(
                    onTap: controller.pickProviderFile,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: primaryColor.withOpacity(0.6),
                        ),
                      ),
                      child: controller.providerFile.value.path.isEmpty
                          ? Row(
                              children: [
                                const Icon(
                                  Icons.add,
                                  size: 40,
                                ),
                                Expanded(
                                  child: Icon(
                                    Icons.image,
                                    color: primaryColor.withOpacity(0.6),
                                    size: Get.width * 0.4,
                                  ),
                                ),
                              ],
                            )
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Image.file(controller.providerFile.value),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
