import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';

import '../controllers/city_and_area_controller.dart';
import '../controllers/language_controller.dart';
import 'custom_drop_down_button.dart';

class AreaAndCityWidget extends StatelessWidget {
  const AreaAndCityWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(
      builder: (signupController) {
        return GetBuilder<CityAndAreaController>(
          builder: (caController) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomDropDownButton(
                  buttonHeight: 50.0.h,
                  buttonWidth: Get.width * 0.4,
                  hint: Get.find<LanguageController>().keys.value.chooseArea!,
                  value: signupController.choosedArea.value.name == null
                      ? null
                      : signupController.choosedArea.value,
                  label: Get.find<LanguageController>().keys.value.area!,
                  onChanged: caController.onChangeArea,
                  items: caController.areas.value.data == null
                      ? null
                      : caController.areas.value.data!
                          .map(
                            (data) => DropdownMenuItem(
                              value: data,
                              child: Text(
                                data.name ?? '',
                              ),
                            ),
                          )
                          .toList(),
                ),
                CustomDropDownButton(
                  buttonHeight: 50.0.h,
                  buttonWidth: Get.width * 0.4,
                  hint: Get.find<LanguageController>().keys.value.chooseCity!,
                  value: signupController.choosedCity.value.name == null
                      ? null
                      : signupController.choosedCity.value,
                  label: Get.find<LanguageController>().keys.value.city!,
                  onChanged: caController.onChangeCity,
                  items: caController.cities.value.data == null
                      ? null
                      : caController.cities.value.data!
                          .map(
                            (data) => DropdownMenuItem(
                              value: data,
                              child: Text(
                                data.name ?? '',
                              ),
                            ),
                          )
                          .toList(),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
