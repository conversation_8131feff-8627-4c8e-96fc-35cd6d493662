import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';

class UserSignupWidget extends StatelessWidget {
  const UserSignupWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(builder: (controller) {
      return const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AreaAndCityWidget(),
        ],
      );
    });
  }
}
