import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'constants.dart';

final bigBlackTextStyle = TextStyle(
  fontSize: 24.sp,
);

final big2BlackTextStyle = TextStyle(
  fontSize: 20.sp,
);

final regularBlackTextStyle = TextStyle(
  fontSize: 14.sp,
);

const middleBlackTextStyle = TextStyle(
  fontSize: 12,
);

final smallBlackTextStyle = TextStyle(
  fontSize: 10.sp,
);

final bigTextStyle = TextStyle(
  color: primaryColor,
  fontSize: 24.sp,
);

final big2TextStyle = TextStyle(
  color: primaryColor,
  fontSize: 20.sp,
);

final regularTextStyle = TextStyle(
  color: primaryColor,
  fontSize: 14.sp,
);

final middleTextStyle = TextStyle(
  color: primaryColor,
  fontSize: 12.sp,
);

final smallTextStyle = TextStyle(
  color: primaryColor,
  fontSize: 10.sp,
);

final bigWhiteTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 24.sp,
);

final big2WhiteTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 20.sp,
);

final regularWhiteTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 14.sp,
);

final middleWhiteTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 12.sp,
);

final smallWhiteTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 10.sp,
);

final middleGreyTextStyle = TextStyle(
  color: Colors.grey,
  fontSize: 12.sp,
);

final smallGreyTextStyle = TextStyle(
  color: Colors.grey,
  fontSize: 10.sp,
);

ThemeData customTheme() {
  return ThemeData.light().copyWith(
    useMaterial3: false,
    primaryColorLight: primaryColor,
    primaryColor: primaryColor,
    colorScheme: ColorScheme.fromSwatch().copyWith(secondary: primaryColor),
    appBarTheme: const AppBarTheme(
      color: primaryColor,
    ),
    scaffoldBackgroundColor: Colors.white,
    textTheme: GoogleFonts.cairoTextTheme().apply(
      bodyColor: Colors.black,
      displayColor: Colors.black,
    ),
    canvasColor: secondaryColor,

    // button theme
    buttonTheme: const ButtonThemeData(
      buttonColor: primaryColor,
      colorScheme: ColorScheme.highContrastLight(),
      textTheme: ButtonTextTheme.normal,
    ),
    dialogTheme: const DialogTheme(),
    drawerTheme: const DrawerThemeData(
      backgroundColor: Colors.white,
    ),

    inputDecorationTheme: const InputDecorationTheme(
      contentPadding: EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 10,
      ),
      border: OutlineInputBorder(),
      iconColor: primaryColor,
    ),
    textButtonTheme: TextButtonThemeData(
      style: ButtonStyle(
        foregroundColor:
            MaterialStateProperty.resolveWith((states) => primaryColor),
        textStyle: MaterialStateProperty.all(
          TextStyle(
            fontSize: 16,
            color: primaryColor,
            fontFamily: GoogleFonts.cairo().fontFamily,
          ),
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        backgroundColor:
            MaterialStateProperty.resolveWith((states) => primaryColor),
        maximumSize: MaterialStateProperty.resolveWith(
          (states) => const Size(600, 70),
        ),
        minimumSize: MaterialStateProperty.resolveWith(
          (states) => const Size(200, 50),
        ),
      ),
    ),
    iconTheme: const IconThemeData(
      color: primaryColor,
    ),

    checkboxTheme: CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) => primaryColor),
      // checkColor: MaterialStateProperty.resolveWith((states) => primaryColor),
    ),
  );
}
