import 'package:flutter/material.dart';

const primaryColor = Color(0xFF28519e);
const secondaryColor = Color(0xFF28519e);
const bgColor = Color(0xFF212332);
var borderColor = Colors.grey[200];

const defaultPadding = 16.0;

// const baseURL = 'https://vishvish-app.com/demo/public/api/'; //? Old Demo
// const baseURL = 'http://vishvish.matrixclouds.net/api/'; //? Matrix Demo
// const baseURL = 'https://vishvish-clean.com/demo/public/api/'; //? Ajory Old Demo

const baseURL = 'https://vishvish-new.digikart.org/api/'; //? New Production
// const baseURL = 'https://vishvish-clean.com/live/public/api/'; //? Production
// const baseURL = 'https://vishvish-demo.digikart.org/api/'; //? Ajory Demo

// * Test Login
//+************ Provider
//********** User

// ********

// cities url
const areasURL = 'districts/';
const citiesURL = 'cities/';
// const citiesURL = 'districts/';

// user urls
const loginURL = 'api-login';
const registerURL = 'api-register';
const verifyURL = 'verify_account';
const verifyAccountURL = 'verify_account';
const resendOTPURL = 'resend-otp';
const forgetPasswordSendPhoneURL = 'forget_password';
const forgetPasswordOTPURL = 'forget_password_otp';
const changePasswordURL = 'change_password';
const editProfileURL = 'edit_profile';
const deleteProfileURL = 'delete_profile';

// get storage keys
const tokenKey = 'token';
const languageKey = 'language';
const userKey = 'user';
const emailOrPhoneKey = 'emailOrPhoneKey';
const passwordKey = 'passwordKey';
const slugKey = 'slugKey';

// global pages url
const privacyPolicyURL = 'page/privacy_policy';
const termsOfServiceURL = 'page/terms_of_service';
const aboutUsURL = 'page/about_us';
const contactInfoURL = 'contact_info';
const faqURL = 'faq';
const homeURL = 'home';

// provider schdeul URLs
const getProviderWeekDays = 'provider/schedule-days';
const getProviderWorkingTimes = 'provider/working-times';
const setProviderWorkingTimes = 'provider/set-working-time';
const deleteProviderWorkingTime = 'provider/delete-working-day/';
const setProviderHoliday = 'provider/set-holiday';
const deleteProviderHoliday = 'provider/delete-holiday/';

// consulation request
const sendFinacialConsultigRequestURL = 'consultations/financial';
const sendLegalAdviceRequestURL = 'consultations/legal';

// services
const allServicesURL = 'services';
const getProviderServicesURL = 'provider/services';
const addNewServiceWithoutTypesURL = 'provider/new-service';
const editServiceWithoutTypesURL = 'provider/edit-service';
const addNewServiceWithTypesURL = 'provider/new-service';
const editServiceWithTypesURL = 'provider/edit-service';
const deleteServiceURL = 'provider/delete-service/';

// skills
const getAllSkillsURL = 'skills';

// offers for provider
const getMyOffersURL = 'provider/offers';
const createNewOfferURL = 'provider/new-offer';
const editOfferURL = 'provider/edit-offer/';
const deleteOfferURL = 'provider/delete-offer/';

// offers for users
const getAllOffersUserURL = 'offers';
const getOfferDetailsURL = 'offers/';
const getOfferAppointmentsURL = 'offer-appointments/';
const bookOfferAppointmentURL = 'book-offer-appointments/';

// service offer
const getServiceOfferDetailsURL = 'provider/service-offers';
const addServiceOfferURL = 'provider/service-offers/add';
// providers
const getAllProvidersURL = 'provider/filter';
// const getAllProvidersURL = 'providers';
const getOfferProvidersURL = 'provider/service-offers';

// booking service
const bookNewServiceURL = 'new-booking';
// const bookNewServiceURL = 'user/new-booking';
const getProviderTimesURL = 'service-time';
const requestPriceURL = 'request-pricing';
const getAllBookingsURL = 'user/my-bookings';
const cancelBookingURL = 'user/cancel-booking/';
const cancelBookingChangeURL = 'user/cancel-order-change/';
const confirmBookingURL = 'user/confirm-booking/';
const approveBookingChangeURL = 'user/approve-order-change/';
const rateBookingURL = 'user/rate-order/';

// request offer
const requestOfferURL = 'user/request-offer';

// const bookingDetailsURL = 'user/my-bookings/';
const payRestOfTotalPriceURL = 'user/booking-rest-payment/';
const addTipURL = 'user/order-tip/';
const acceptExtraTimeURL = 'user/confirm-provider-pricing-request/';
const rejectExtraTimeURL = 'user/reject-provider-pricing-request/';
const requestInvoiceURL = 'user/request-invoice/';

// provider orders
const getProviderOrdersByStatusURL = 'provider/order-by-status';
const getAllProviderOrdersURL = 'provider/orders';
const getFutureByGroupIdURL = 'provider/get-by-group/';
const getProviderAllOrdersURL = 'provider/all-orders';
const cancelOrderByProviderURL = 'provider/cancel-order/';
const approveOrderByProviderURL = 'provider/approve-order/';
const approveClothOrderByProviderURL = 'provider/approve_cloth_order/';
const confirmClothOrderByProviderURL = 'provider/confirm_receiving_clothes/';
const editClothOrderByProviderURL = 'provider/update-order/';
const compeleteOrderByProviderURL = 'provider/complete-order/';
const submitNewOfferURL = 'provider/change-order-pricing-option/';
const updateTrackingStatusURL = 'provider/update-tracking-status/';
const clothFinishedURL = 'provider/clothes-clean-finished/';

// Provider Cart Orders
// const getNeedActionOrders = 'user/my-booking/need-action';
// const getTodoOrders = 'user/my-booking/to-do';

// Provider Cart Orders
const getOrdersNeedActionOrders = 'provider/my-order/need-action';
const getOrdersTodoOrders = 'provider/my-order/to-do';

// User Cart Orders
const getNeedActionOrders = 'user/orders/need-action';
const getTodoOrders = 'user/orders/to-do';

const getFutureByUserGroupIdURL = 'user/get-by-group/';

// notifications
const allNotificationsURL = 'notifications';
const markNotificationAsReadedURL = 'read-notification';

// wallet
const providerWalletURL = 'provider/wallet';
const uploadInvoiceURL = 'provider/upload-invoice';

// user Wallet
const userWalletURL = 'user/billings';

// provider reivews
const providerReviewsURL = 'provider/reviews';

// fcm token
const updateFCMTokenURL = 'update_fcm_token';

// chat
const sendMessageURL = 'send-message';
const getOrderMessages = 'chat';
const locationsUrl = 'locations';
const editLocationsUrl = 'edit-locations';
const deleteLocationsUrl = 'delete-location';

const getJobsUrl = 'mobile-job-applications';
const addJobs = 'mobile-job-applications-daily';
const deleteJobs = 'mobile-job-applications/delete';

// aurgent orders
const sendAurgentRequestURL = 'user/new-urgent-booking';

// languages
const getLanguagesURL = 'languages';

// albums
const getAlbums = 'provider/albums';

const addNewAlbumURL = 'provider/album/add-new';
const editAlbumURL = 'provider/album/update/';
const deleteAlbumURL = 'provider/album/delete/';

// images
const getAlbumImagesURL = 'provider/album-photo/';
const addNewImageURL = 'provider/album-photo/add-new';
const deleteImageURL = 'provider/album-photo/delete/';

// favorites
const getFavoritesURL = 'user/all-favorites';
const addNewFavoriteURL = 'user/add-to-favorites';

// provider service offers
const getPendingProviderServiceOffersURL = 'provider/offers-request/pending';
const getWaitingProviderServiceOffersURL =
    'provider/offers-request/waiting-to-visit';
const getUnpaidProviderServiceOffersURL = 'provider/offers-request/unpaid';

// user service offers
const getPendingUserServiceOffersURL = 'user/offers-request/pending';
const getWaitingUserServiceOffersURL = 'user/offers-request/waiting-to-visit';
const getUnpaidUserServiceOffersURL = 'user/offers-request/unpaid';

const approveProviderServiceOffersURL = 'provider/offers-request/approve';
const confirmProviderServiceOffersURL =
    'provider/offers-request/confirm-book-date';
