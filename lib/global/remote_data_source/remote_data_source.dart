import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';

class CitiesAndAreasRemoteDataSource {
  DioHelper helper = DioHelper();

  Future<dynamic> getAllAreas() async {
    try {
      final response = await helper.getData(areasURL);
      return response;
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<dynamic> getAllCities(int areaId) async {
    try {
      final response = await helper.getData(
        citiesURL,
        params: {'district': areaId.toString()},
      );
      // .getData(areasURL, params: {'district': areaId.toString()});
      return response;
    } catch (e) {
      log(e.toString());
      return null;
    }
  }
}
