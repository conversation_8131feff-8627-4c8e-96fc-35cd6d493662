import 'dart:convert';
import 'dart:developer';

import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/global_states/about_us_state.dart';
import 'package:get_clean/global/global_states/all_offers_state.dart';
import 'package:get_clean/global/global_states/contact_info_state.dart';
import 'package:get_clean/global/global_states/faq_states.dart';
import 'package:get_clean/global/global_states/home_states.dart';
import 'package:get_clean/global/global_states/services_state.dart';
import 'package:get_clean/global/global_states/skills_state.dart';
import 'package:get_clean/global/models/about_us_model.dart';
import 'package:get_clean/global/models/all_offers_model.dart';
import 'package:get_clean/global/models/all_services_model.dart';
import 'package:get_clean/global/models/faq_model.dart';
import 'package:get_clean/global/models/home_model.dart';
import 'package:get_clean/global/models/skills_model.dart';

import '../global_states/privacy_policy_state.dart';
import '../global_states/terms_of_service_state.dart';
import '../models/contact_info_model.dart';
import '../models/privacy_policy_model.dart';
import '../models/terms_of_service.dart';

class GlobalValuesRemoteDataSource {
  DioHelper helper = DioHelper();

  Future<HomeState> getHome() async {
    try {
      final response = await helper.getData(homeURL,
          params: {if (isLoggedIn()) 'user_id': currentUserId()});

      if (response['success'] == true) {
        log('ResponseHomeDataa ${response}');
        return HomeSuccessState(HomeModel.fromJson(response));
      } else {
        return HomeErrorState(response.toString());
      }
    } catch (e, s) {
      log("Home Data Error $e\n$s");
      return HomeErrorState(e.toString());
    }
  }

  Future<AllOffersState> getAllOffers() async {
    try {
      final response = await helper.getData(getAllOffersUserURL);

      if (response['success'] == true) {
        return AllOffersSuccessState(AllOffersModel.fromJson(response));
      } else {
        return AllOffersErrorState(response.toString());
      }
    } catch (e) {
      log(e.toString());
      return AllOffersErrorState(e.toString());
    }
  }

  // Future<ProvidersState> getAllProviders() async {
  //   try {
  //     final response = await helper.getData(getAllProvidersURL);

  //     if (response['success'] == true) {
  //       return ProvidersSuccessState(Providers.fromJson(response));
  //     } else {
  //       return ProvidersErrorState(response.toString());
  //     }
  //   } catch (e) {
  //     log(e.toString());
  //     return ProvidersErrorState(e.toString());
  //   }
  // }

  Future<FAQState> getFAQ() async {
    try {
      final response = await helper.getData(faqURL);

      if (response['success'] == true) {
        return FAQSuccessState(FAQModel.fromJson(response));
      } else {
        return FAQErrorState(response.toString());
      }
    } catch (e) {
      log(e.toString());
      return FAQErrorState(e.toString());
    }
  }

  Future<SkillsState> getAllSkills() async {
    try {
      final response = await helper.getData(getAllSkillsURL);
      log("All Skills ${jsonEncode(response)}");
      if (response['success'] == true) {
        return SkillsSuccessState(SkillsModel.fromJson(response));
      } else {
        return SkillsErrorState(response.toString());
      }
    } catch (e) {
      log(e.toString());
      return SkillsErrorState(e.toString());
    }
  }

  Future<ServicesState> getAllServices({int? providerId}) async {
    final url = providerId != null ? getProviderServicesURL : allServicesURL;
    try {
      final response = await helper.getData(url, params: {
        if (providerId != null) 'user_id': providerId,
      });

      log('ffffaffasf $response');

      if (response['success'] == true) {
        return ServicesSuccessState(AllServicesModel.fromJson(response));
      } else {
        return ServicesErrorState(response.toString());
      }
    } catch (e, s) {
      log(e.toString() + s.toString());
      return ServicesErrorState(e.toString());
    }
  }

  Future<AboutUsState> getAboutUsData() async {
    try {
      final response = await helper.getData(aboutUsURL);

      if (response['success'] == true) {
        return AboutUsSuccessState(AboutUsModel.fromJson(response));
      } else {
        return AboutUsErrorState(response.toString());
      }
    } catch (e) {
      return AboutUsErrorState(e.toString());
    }
  }

  Future<ContactInfoState> getContactInfoData() async {
    try {
      final response = await helper.getData(contactInfoURL);

      if (response['success'] == true) {
        return ContactInfoSuccessState(ContactInfoModel.fromJson(response));
      } else {
        return ContactInfoErrorState(response.toString());
      }
    } catch (e) {
      return ContactInfoErrorState(e.toString());
    }
  }

  Future<PrivacyPolicyState> getPrivacyPolicyData() async {
    try {
      final response = await helper.getData(privacyPolicyURL);

      if (response['success'] == true) {
        return PrivacyPolicySuccessState(PrivacyPolicyModel.fromJson(response));
      } else {
        return PrivacyPolicyErrorState(response.toString());
      }
    } catch (e) {
      return PrivacyPolicyErrorState(e.toString());
    }
  }

  Future<TermsOfServiceState> getTermsOfServiceData() async {
    try {
      final response = await helper.getData(termsOfServiceURL);

      if (response['success'] == true) {
        // log(response.toString());
        return TermsOfServiceSuccessState(
            TermsOfServiceModel.fromJson(response));
      } else {
        return TermsOfServiceErrorState(response);
      }
    } catch (e) {
      return TermsOfServiceErrorState(e.toString());
    }
  }
}
