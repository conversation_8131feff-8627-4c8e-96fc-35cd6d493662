import 'package:get_clean/global/models/user_booking.dart';

class NotificationsModel {
  bool? success;
  int? code;
  List<NotificationData>? data = [];

  NotificationsModel({this.success, this.code, this.data});

  NotificationsModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    if (json['data'] != null) {
      data = <NotificationData>[];
      json['data'].forEach((v) {
        data!.add(NotificationData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NotificationData {
  String? id;
  String? title;
  String? body;

  NotificationDetails? data;
  String? sentAt;
  bool? isRead;

  NotificationData(
      {this.id,
      this.title,
      this.body,
      this.data,
      this.sentAt,
      this.isRead = false});

  NotificationData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title =
        (json['title'] == null || json['title'] == '') && json['data'] != null
            ? json['data']['title']
            : json['title'];
    data = json['data'] != null
        ? NotificationDetails.fromJson(json['data'])
        : null;
    sentAt = json['sent_at'];
    isRead = json['is_read'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['sent_at'] = sentAt;
    data['is_read'] = isRead;
    return data;
  }
}

class NotificationDetails {
  String? type;
  String? body;
  BookingData? details;

  NotificationDetails({this.type, this.details});

  NotificationDetails.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    body = json['body'];
    details =
        json['details'] != null ? BookingData.fromJson(json['details']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (details != null) {
      data['details'] = details!.toJson();
    }
    return data;
  }
}
