import 'dart:developer';

import 'package:get_clean/global/models/car_model.dart';

import 'city.dart';

class ProviderServices {
  int? id;
  String? name;
  String? image;
  PricingOption? pricingOption;
  num? materialPrice;
  List<PricingList>? pricingList;
  num? minHours;
  bool? deliver;
  List<TypesSettings>? typesSettings = [];
  bool? withTax;
  List<CarModel>? carServices;

  //? For My Services
  ProviderServices? service;

  //? For Home
  List<ProviderServices>? services;
  int? pricingOptionId; //? 1 -> Hour, 2 -> Meter

  ProviderServices(
      {this.id,
      this.name,
      this.image,
      this.pricingOption,
      this.materialPrice,
      this.minHours,
      this.deliver,
      this.pricingList,
      this.typesSettings,
      this.withTax,
      this.carServices,
      this.pricingOptionId,
      this.service});

  ProviderServices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    deliver = json['deliver'];
    minHours = num.tryParse(json['min_hours']?.toString() ?? '2') ?? 2;
    service = json['service'] != null
        ? ProviderServices.fromJson(json['service'])
        : null;
    if (json['services'] != null) {
      if (json['services'].runtimeType == List) {
        services = <ProviderServices>[];
        json['services'].forEach((v) {
          services!.add(ProviderServices.fromJson(v));
        });
      } else {
        services = <ProviderServices>[];
        services!.add(ProviderServices.fromJson(json['services']));
        service = ProviderServices.fromJson(json['services']);

        log('PROVIDER_SERVIffCESS -> ${json['services']}');
        log('PROVIDER_SERVIfgggggfCESS -> ${service?.toJson()}');
      }
    }

    pricingOption = json['pricing_option'] == null
        ? service?.pricingOption
        : PricingOption.fromJson(json['pricing_option']);
    materialPrice = num.tryParse(json['material_price'].toString()) ?? 0;
    if (json['pricing_list'] != null) {
      pricingList = <PricingList>[];
      json['pricing_list'].forEach((v) {
        pricingList!.add(PricingList.fromJson(v));
      });
    } else {
      pricingList = service?.pricingList;
    }
    if (json['types_settings'] != null) {
      for (var type in json['types_settings']) {
        typesSettings!.add(TypesSettings.fromJson(type));
      }
    } else {
      typesSettings = service?.typesSettings;
    }
    withTax = json['with_tax'];
    if (json['carServices'] != null) {
      //TODO-Check
      carServices = <CarModel>[];
      json['carServices'].forEach((v) {
        carServices!.add(CarModel.fromJson(v));
      });
    } else {
      carServices = service?.carServices;
    }

    pricingOptionId =
        json['pricing_option'] != null ? json['pricing_option']['id'] : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    data['min_hours'] = minHours;
    data['deliver'] = deliver;

    data['material_price'] = materialPrice;
    if (service != null) {
      data['service'] = service!.toJson();
    }

    if (pricingList != null) {
      data['pricing_list'] = pricingList!.map((v) => v.toJson()).toList();
    } else if (service != null && service!.pricingList != null) {
      data['pricing_list'] =
          service!.pricingList!.map((v) => v.toJson()).toList();
    }
    if (typesSettings != null) {
      data['types_settings'] = typesSettings!.map((v) => v.toJson()).toList();
    } else if (service != null && service!.typesSettings != null) {
      data['types_settings'] =
          service!.typesSettings!.map((v) => v.toJson()).toList();
    }
    data['with_tax'] = withTax;
    if (carServices != null) {
      data['carServices'] = carServices!.map((v) => v.toJson()).toList();
    } else if (service != null && service!.carServices != null) {
      data['carServices'] =
          service!.carServices!.map((v) => v.toJson()).toList();
    }

    if (services != null) {
      data['services'] = services!.map((v) => v.toJson()).toList();
    }

    if (pricingOption == null) {
      if (service != null && service!.pricingOption != null) {
        data['pricing_option'] = service!.pricingOption!.toJson();
      }
    } else {
      data['pricing_option'] = pricingOption!.toJson();
    }

    if (pricingOptionId == null) {
      data['pricing_option_id'] = service?.pricingOption?.id;
    } else {
      data['pricing_option_id'] = pricingOptionId;
    }

    return data;
  }
}

class PricingOption {
  int? id;
  String? name;
  bool? hasTypes;
  List<OptionTypes>? optionTypes;

  PricingOption({this.id, this.name, this.hasTypes, this.optionTypes});

  PricingOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    hasTypes = json['has_types'];
    if (json['option_types'] != null) {
      optionTypes = <OptionTypes>[];
      json['option_types'].forEach((v) {
        optionTypes!.add(OptionTypes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['has_types'] = hasTypes;
    if (optionTypes != null) {
      data['option_types'] = optionTypes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PricingList {
  num? from;
  num? to;
  num? price;
  CarModel? typeModel;

  // OptionTypes? type;

  PricingList({this.from, this.to, this.price});

  PricingList.fromJson(Map<String, dynamic> json) {
    from = json['from'];
    to = json['to'];

    price = num.tryParse(json['price'].toString()) ?? 0;

    if (json['type'] != null) {
      final typeData = json['type'];
      typeModel = CarModel.fromJson(typeData);
      // type = OptionTypes.fromJson(typeData);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['from'] = from;
    data['to'] = to;
    data['price'] = price;

    if (typeModel != null) {
      data['type'] = typeModel!.toJson();
    }
    return data;
  }
}

class WorkAreas {
  int? id;
  String? name;
  City? area;

  WorkAreas({this.id, this.name, this.area});

  WorkAreas.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    area = json['district'] != null ? City.fromJson(json['district']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (area != null) {
      data['area'] = area!.toJson();
    }
    return data;
  }
}

class WorkingTime {
  int? id;
  String? day;
  String? dayName;
  String? startsAt;
  String? endsAt;

  WorkingTime({this.id, this.day, this.dayName, this.startsAt, this.endsAt});

  WorkingTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    day = json['day'];
    dayName = json['day_name'];
    startsAt = json['starts_at'];
    endsAt = json['ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['day'] = day;
    data['day_name'] = dayName;
    data['starts_at'] = startsAt;
    data['ends_at'] = endsAt;
    return data;
  }

  //? copyWith
  WorkingTime copyWith({
    int? id,
    String? day,
    String? dayName,
    String? startsAt,
    String? endsAt,
  }) {
    return WorkingTime(
      id: id ?? this.id,
      day: day ?? this.day,
      dayName: dayName ?? this.dayName,
      startsAt: startsAt ?? this.startsAt,
      endsAt: endsAt ?? this.endsAt,
    );
  }
}

class Holidays {
  int? id;
  String? startsAt;
  String? endsAt;

  Holidays({this.id, this.startsAt, this.endsAt});

  Holidays.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startsAt = json['starts_at'];
    endsAt = json['ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['starts_at'] = startsAt;
    data['ends_at'] = endsAt;
    return data;
  }

  //? copyWith
  Holidays copyWith({
    int? id,
    String? startsAt,
    String? endsAt,
  }) {
    return Holidays(
      id: id ?? this.id,
      startsAt: startsAt ?? this.startsAt,
      endsAt: endsAt ?? this.endsAt,
    );
  }
}

class OptionTypes {
  int? id;
  String? name;
  String? price;
  List<CarModel>? carServices;

  OptionTypes({this.id, this.name});

  OptionTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price']?.toString();
    if (json['carServices'] != null) {
      carServices = <CarModel>[];
      json['carServices'].forEach((v) {
        carServices!.add(CarModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (carServices != null) {
      data['carServices'] = carServices!.map((v) => v.toJson()).toList();
    }
    data['price'] = price;
    return data;
  }
}

class TypesSettings {
  int? id;
  String? name;
  int? estimatedDuration;
  num? hours;

  TypesSettings({
    this.id,
    this.name,
    this.estimatedDuration,
    this.hours,
  });

  TypesSettings.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    estimatedDuration = json['estimated_duration'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['estimated_duration'] = estimatedDuration;
    return data;
  }
}
