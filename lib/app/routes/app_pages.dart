import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/bindings/album_binding.dart';
import 'package:get_clean/app/modules/albums/views/album_view.dart';
import 'package:get_clean/app/modules/favorites/bindings/favorites_binding.dart';
import 'package:get_clean/app/modules/favorites/views/favorites_view.dart';
import 'package:get_clean/app/modules/home/<USER>/all_home_view.dart';
import 'package:get_clean/app/modules/my_cart/bindings/my_cart_binding.dart';
import 'package:get_clean/app/modules/my_cart/views/cart/my_cart_view.dart';
import 'package:get_clean/app/modules/my_orders/views/booking/my_booking_view.dart';
import 'package:get_clean/app/modules/order_details/views/calculate_offer_page/calculate_offer_page.dart';
import 'package:get_clean/app/modules/provider_offer_services/bindings/provider_offer_services_binding.dart';
import 'package:get_clean/app/modules/provider_offer_services/views/provider_offers_services_view.dart';
import 'package:get_clean/app/modules/provider_page/views/provider_page_view_from_dynamic_link.dart';
import 'package:get_clean/app/modules/user_offer_services/bindings/user_offer_services_binding.dart';
import 'package:get_clean/app/modules/user_offer_services/views/user_offers_services_view.dart';
import 'package:get_clean/app/offer_services/bindings/calculate_offer_service_binding.dart';
import 'package:get_clean/main.dart';

import '../modules/about_us/bindings/about_us_binding.dart';
import '../modules/about_us/views/about_us_view.dart';
import '../modules/add_new_service/bindings/add_new_service_binding.dart';
import '../modules/add_new_service/views/add_new_service_view.dart';
import '../modules/add_offer/bindings/add_offer_binding.dart';
import '../modules/add_offer/views/add_offer_view.dart';
import '../modules/all_popular_services/bindings/all_popular_services_binding.dart';
import '../modules/all_popular_services/views/all_popular_services_view.dart';
import '../modules/all_services/bindings/all_services_binding.dart';
import '../modules/all_services/views/all_services_view.dart';
import '../modules/change_password/bindings/change_password_binding.dart';
import '../modules/change_password/views/change_password_view.dart';
import '../modules/chat/bindings/chat_binding.dart';
import '../modules/chat/views/chat_view.dart';
import '../modules/congratulation_page/bindings/congratulation_page_binding.dart';
import '../modules/congratulation_page/views/congratulation_page_view.dart';
import '../modules/consulation_request/bindings/consulation_request_binding.dart';
import '../modules/consulation_request/views/consulation_request_view.dart';
import '../modules/edit_offer/bindings/edit_offer_binding.dart';
import '../modules/edit_offer/views/edit_offer_view.dart';
import '../modules/edit_schduel/bindings/edit_schduel_binding.dart';
import '../modules/edit_schduel/views/edit_schduel_view.dart';
import '../modules/edit_service/bindings/edit_service_binding.dart';
import '../modules/edit_service/views/edit_service_view.dart';
import '../modules/enter_code_forget_password/bindings/enter_code_forget_password_binding.dart';
import '../modules/enter_code_forget_password/views/enter_code_forget_password_view.dart';
import '../modules/enter_new_password_forget_password/bindings/enter_new_password_forget_password_binding.dart';
import '../modules/enter_new_password_forget_password/views/enter_new_password_forget_password_view.dart';
import '../modules/enter_phone_number/bindings/enter_phone_number_binding.dart';
import '../modules/enter_phone_number/views/enter_phone_number_view.dart';
import '../modules/faq/bindings/faq_binding.dart';
import '../modules/faq/views/faq_view.dart';
import '../modules/filter_orders/bindings/filter_orders_binding.dart';
import '../modules/filter_orders/views/filter_orders_view.dart';
import '../modules/jobs/bindings/jobs_binding.dart';
import '../modules/jobs/views/jobs_view.dart';
import '../modules/jobs/views/job_applications_view.dart';
import '../modules/my_profile/views/work_location_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/languages/bindings/languages_binding.dart';
import '../modules/languages/views/languages_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/my_booking_details/bindings/my_booking_details_binding.dart';
import '../modules/my_booking_details/views/my_booking_details_view.dart';
import '../modules/my_offers/bindings/my_offers_binding.dart';
import '../modules/my_offers/views/my_offers_view.dart';
import '../modules/my_orders/bindings/my_booking_binding.dart';
import '../modules/my_orders/bindings/my_orders_binding.dart';
import '../modules/my_orders/views/orders/my_orders_view.dart';
import '../modules/my_profile/bindings/my_profile_binding.dart';
import '../modules/my_profile/views/my_profile_view.dart';
import '../modules/notifications/bindings/notifications_binding.dart';
import '../modules/notifications/views/notifications_view.dart';
import '../modules/offer_details/bindings/offer_details_binding.dart';
import '../modules/offer_details/views/offer_details_view.dart';
import '../modules/offers/bindings/offers_binding.dart';
import '../modules/offers/views/offers_view.dart';
import '../modules/order_approved/bindings/order_approved_binding.dart';
import '../modules/order_approved/views/order_approved_view.dart';
import '../modules/order_canceled/bindings/order_canceled_binding.dart';
import '../modules/order_canceled/views/order_canceled_view.dart';
import '../modules/order_compeleted/bindings/order_compeleted_binding.dart';
import '../modules/order_compeleted/views/order_compeleted_view.dart';
import '../modules/order_details/bindings/order_details_binding.dart';
import '../modules/order_details/views/order_details_view.dart';
import '../modules/password_changed_successfully/bindings/password_changed_successfully_binding.dart';
import '../modules/password_changed_successfully/views/password_changed_successfully_view.dart';
import '../modules/payment/bindings/payment_binding.dart';
import '../modules/payment/views/payment_view.dart';
import '../modules/payment_compeleted/bindings/payment_compeleted_binding.dart';
import '../modules/payment_compeleted/views/payment_compeleted_view.dart';
import '../modules/payment_webview/bindings/payment_webview_binding.dart';
import '../modules/payment_webview/views/payment_webview_view.dart';
import '../modules/pricing/bindings/pricing_binding.dart';
import '../modules/pricing/views/pricing_view.dart';
import '../modules/privacy_policy/bindings/privacy_policy_binding.dart';
import '../modules/privacy_policy/views/privacy_policy_view.dart';
import '../modules/provider_from_slider/bindings/provider_from_slider_binding.dart';
import '../modules/provider_from_slider/views/provider_from_slider_view.dart';
import '../modules/provider_page/bindings/provider_page_binding.dart';
import '../modules/provider_page/views/provider_page_view.dart';
import '../modules/provider_page_filtered/bindings/provider_page_filtered_binding.dart';
import '../modules/provider_page_filtered/views/provider_page_filtered_view.dart';
import '../modules/provider_reviews/bindings/provider_reviews_binding.dart';
import '../modules/provider_reviews/views/provider_reviews_view.dart';
import '../modules/rate_provider/bindings/rate_provider_binding.dart';
import '../modules/rate_provider/views/rate_provider_view.dart';
import '../modules/request_sent_successfully/bindings/request_sent_successfully_binding.dart';
import '../modules/request_sent_successfully/views/request_sent_successfully_view.dart';
import '../modules/service_providers/bindings/service_providers_binding.dart';
import '../modules/service_providers/views/service_providers_view.dart';
import '../modules/signup/bindings/signup_binding.dart';
import '../modules/signup/views/signup_view.dart';
import '../modules/splash_screen/bindings/splash_screen_binding.dart';
import '../modules/splash_screen/views/splash_screen_view.dart';
import '../modules/terms_and_conditions/bindings/terms_and_conditions_binding.dart';
import '../modules/terms_and_conditions/views/terms_and_conditions_view.dart';
import '../modules/user_wallet/bindings/user_wallet_binding.dart';
import '../modules/user_wallet/views/user_wallet_view.dart';
import '../modules/verification_code/bindings/verification_code_binding.dart';
import '../modules/verification_code/views/verification_code_view.dart';
import '../modules/wallet/bindings/wallet_binding.dart';
import '../modules/wallet/views/wallet_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.SPLASH_SCREEN;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.ALL_HOME_VIEW,
      page: () => const AllHomeView(),
    ),
    GetPage(
      name: _Paths.FAVORITES,
      page: () => const FavoritesView(),
      binding: FavoriteBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH_SCREEN,
      page: () => const SplashScreenView(),
      binding: SplashScreenBinding(),
    ),
    GetPage(
      name: _Paths.ALBUMS,
      page: () => const AlbumsView(),
      binding: AlbumSBinding(),
    ),
    GetPage(
      name: _Paths.MAIN_PAGE,
      page: () => const MainPage(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.SIGNUP,
      page: () => const SignupView(),
      binding: SignupBinding(),
    ),
    GetPage(
      name: _Paths.VERIFICATION_CODE,
      page: () => const VerificationCodeView(),
      binding: VerificationCodeBinding(),
    ),
    GetPage(
      name: _Paths.CONGRATULATION_PAGE,
      page: () => const CongratulationPageView(),
      binding: CongratulationPageBinding(),
    ),
    GetPage(
      name: _Paths.MY_ORDERS,
      page: () => const MyOrdersView(),
      binding: MyOrdersBinding(),
    ),
    GetPage(
      name: _Paths.OFFERS,
      page: () => const OffersView(),
      binding: OffersBinding(),
    ),
    GetPage(
      name: _Paths.OFFER_SERVICE,
      page: () => const UserOfferServicesView(),
      binding: UserOfferServicesBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_OFFER_SERVICE,
      page: () => const ProviderOfferServicesView(),
      binding: ProviderOfferServicesBinding(),
    ),
    GetPage(
      name: _Paths.CALCULATE_OFFER_PAGE,
      page: () => const CalculateOfferPage(),
      binding: CalculateOfferServicesBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATIONS,
      page: () => const NotificationsView(),
      binding: NotificationsBinding(),
    ),
    GetPage(
      name: _Paths.MY_CART,
      page: () => const MyCartsView(),
      binding: MyCartBinding(),
    ),
    GetPage(
      name: _Paths.SERVICE_PROVIDERS,
      page: () => const ServiceProvidersView(),
      binding: ServiceProvidersBinding(),
    ),
    GetPage(
      name: _Paths.ALL_POPULAR_SERVICES,
      page: () => const AllPopularServicesView(),
      binding: AllPopularServicesBinding(),
    ),
    GetPage(
      name: _Paths.ALL_SERVICES,
      page: () => const AllServicesView(),
      binding: AllServicesBinding(),
    ),
    GetPage(
      name: _Paths.ABOUT_US,
      page: () => const AboutUsView(),
      binding: AboutUsBinding(),
    ),
    GetPage(
      name: _Paths.PRIVACY_POLICY,
      page: () => const PrivacyPolicyView(),
      binding: PrivacyPolicyBinding(),
    ),
    GetPage(
      name: _Paths.TERMS_AND_CONDITIONS,
      page: () => const TermsAndConditionsView(),
      binding: TermsAndConditionsBinding(),
    ),
    GetPage(
      name: _Paths.CONSULATION_REQUEST,
      page: () => const ConsulationRequestView(),
      binding: ConsulationRequestBinding(),
    ),
    GetPage(
      name: _Paths.FAQ,
      page: () => const FaqView(),
      binding: FaqBinding(),
    ),
    GetPage(
      name: _Paths.LANGUAGES,
      page: () => const LanguagesView(),
      binding: LanguagesBinding(),
    ),
    GetPage(
      name: _Paths.MY_BOOKING,
      page: () => const MyBookingsView(),
      binding: MyBookingBinding(),
    ),
    GetPage(
      name: _Paths.MY_BOOKING_DETAILS,
      page: () => const MyBookingDetailsView(),
      binding: MyBookingDetailsBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT,
      page: () => const PaymentView(),
      binding: PaymentBinding(),
    ),
    // GetPage(
    //   name: _Paths.PAYMENT_METHOD,
    //   page: () => const PaymentMethodView(),
    //   binding: PaymentMethodBinding(),
    // ),
    GetPage(
      name: _Paths.PAYMENT_COMPELETED,
      page: () => const PaymentCompletedView(),
      binding: PaymentCompeletedBinding(),
    ),
    GetPage(
      name: _Paths.RATE_PROVIDER,
      page: () => const RateProviderView(),
      binding: RateProviderBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_CANCELED,
      page: () => const OrderCanceledView(),
      binding: OrderCanceledBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_DETAILS,
      page: () => const OrderDetailsView(),
      binding: OrderDetailsBinding(),
    ),
    GetPage(
      name: _Paths.WALLET,
      page: () => const WalletView(),
      binding: WalletBinding(),
    ),
    GetPage(
      name: _Paths.ENTER_PHONE_NUMBER,
      page: () => const EnterPhoneNumberView(),
      binding: EnterPhoneNumberBinding(),
    ),
    GetPage(
      name: _Paths.ENTER_CODE_FORGET_PASSWORD,
      page: () => const EnterCodeForgetPasswordView(),
      binding: EnterCodeForgetPasswordBinding(),
    ),
    GetPage(
      name: _Paths.ENTER_NEW_PASSWORD_FORGET_PASSWORD,
      page: () => const EnterNewPasswordForgetPasswordView(),
      binding: EnterNewPasswordForgetPasswordBinding(),
    ),
    GetPage(
      name: _Paths.PASSWORD_CHANGED_SUCCESSFULLY,
      page: () => const PasswordChangedSuccessfullyView(),
      binding: PasswordChangedSuccessfullyBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_PAGE,
      page: () => const ProviderPageView(),
      binding: ProviderPageBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_PAGE_FROM_DYNAMIC_LINK,
      page: () => const ProviderPageFromDynamicLinkView(),
      binding: ProviderPageBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_REVIEWS,
      page: () => const ProviderReviewsView(),
      binding: ProviderReviewsBinding(),
    ),
    GetPage(
      name: _Paths.MY_PROFILE,
      page: () => const MyProfileView(),
      binding: MyProfileBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_PAGE_FILTERED,
      page: () => const ProviderPageFilteredView(),
      binding: ProviderPageFilteredBinding(),
    ),
    GetPage(
      name: _Paths.REQUEST_SENT_SUCCESSFULLY,
      page: () => const RequestSentSuccessfullyView(),
      binding: RequestSentSuccessfullyBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_SCHDUEL,
      page: () => const EditSchduelView(),
      binding: EditSchduelBinding(),
    ),
    GetPage(
      name: _Paths.CHANGE_PASSWORD,
      page: () => const ChangePasswordView(),
      binding: ChangePasswordBinding(),
    ),
    GetPage(
      name: _Paths.PRICING,
      page: () => const PricingView(),
      binding: PricingBinding(),
    ),
    GetPage(
      name: _Paths.ADD_NEW_SERVICE,
      page: () => const AddNewServiceView(),
      binding: AddNewServiceBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_SERVICE,
      page: () => const EditServiceView(),
      binding: EditServiceBinding(),
    ),
    GetPage(
      name: _Paths.MY_OFFERS,
      page: () => const MyOffersView(),
      binding: MyOffersBinding(),
    ),
    GetPage(
      name: _Paths.ADD_OFFER,
      page: () => const AddOfferView(),
      binding: AddOfferBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_OFFER,
      page: () => const EditOfferView(),
      binding: EditOfferBinding(),
    ),
    GetPage(
      name: _Paths.OFFER_DETAILS,
      page: () => const OfferDetailsView(),
      binding: OfferDetailsBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_APPROVED,
      page: () => const OrderApprovedView(),
      binding: OrderApprovedBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_COMPELETED,
      page: () => const OrderCompeletedView(),
      binding: OrderCompeletedBinding(),
    ),
    GetPage(
      name: _Paths.CHAT,
      page: () => const ChatView(),
      binding: ChatBinding(),
    ),
    GetPage(
      name: _Paths.USER_WALLET,
      page: () => const UserWalletView(),
      binding: UserWalletBinding(),
    ),
    GetPage(
      name: _Paths.PROVIDER_FROM_SLIDER,
      page: () => const ProviderFromSliderView(),
      binding: ProviderFromSliderBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_WEBVIEW,
      page: () => const PaymentWebviewView(),
      binding: PaymentWebviewBinding(),
    ),
    GetPage(
      name: _Paths.FILTER_ORDERS,
      page: () => const FilterOrdersView(),
      binding: FilterOrdersBinding(),
    ),
    GetPage(
      name: _Paths.WORK_LOCATION_VIEW,
      page: () => const WorkLocationView(),
    ),
    GetPage(
      name: _Paths.JOBS_VIEW,
      page: () => const JobsView(),
      binding: JobsBinding(),
    ),
    GetPage(
      name: _Paths.JOB_APPLICATIONS_VIEW,
      page: () => const JobApplicationsView(),
      binding: JobsBinding(),
    ),
  ];
}
