import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../global/models/user_booking.dart';
import '../../../routes/app_pages.dart';

class PaymentWebviewController extends GetxController {
  final BookingData order = Get.arguments['order'];
  final bool payDeposit = Get.arguments['payDeposit'];
  final tip = Get.arguments['tip'];
  final url = Get.arguments['url'];
  late final WebViewController controller;

  @override
  void onInit() {
    super.onInit();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            if (url.contains('Successful-Payment')) {
              gotoPaymentCompeleted();
            } else if (url.contains('Failed-Payment')) {
              backToPaymentPage();
            }
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(url));
  }

  void gotoPaymentCompeleted() {
    Get.toNamed(
      Routes.PAYMENT_COMPELETED,
      arguments: {
        'order': order,
        'payDeposit': payDeposit,
        'tip': tip,
      },
    );
  }

  void backToPaymentPage() {
    Get.back();
  }
}
