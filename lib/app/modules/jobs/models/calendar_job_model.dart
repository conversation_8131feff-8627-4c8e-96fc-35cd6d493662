class CalendarJobsResponse {
  final bool success;
  final String month;
  final List<CalendarJob> data;

  CalendarJobsResponse({
    required this.success,
    required this.month,
    required this.data,
  });

  factory CalendarJobsResponse.fromJson(Map<String, dynamic> json) {
    return CalendarJobsResponse(
      success: json['success'] ?? false,
      month: json['month'] ?? '',
      data: json['data'] != null
          ? List<CalendarJob>.from(json['data'].map((x) => CalendarJob.fromJson(x)))
          : [],
    );
  }
}

class CalendarJob {
  final String type; // "order" or "job_application"
  final int? orderId;
  final int? jobApplicationId;
  final int? dId;
  final String date;
  final String day;
  final String startTime;
  final String endTime;
  final String status;
  final Task? task;

  CalendarJob({
    required this.type,
    this.orderId,
    this.jobApplicationId,
    this.dId,
    required this.date,
    required this.day,
    required this.startTime,
    required this.endTime,
    required this.status,
    this.task,
  });

  factory CalendarJob.fromJson(Map<String, dynamic> json) {
    return CalendarJob(
      type: json['type'] ?? '',
      orderId: json['order_id'],
      jobApplicationId: json['job_application_id'],
      dId: json['d_id'],
      date: json['date'] ?? '',
      day: json['day'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      status: json['status'] ?? '',
      task: json['task'] != null ? Task.fromJson(json['task']) : null,
    );
  }

  // Helper method to get parsed date
  DateTime get parsedDate {
    try {
      if (type == 'order') {
        return DateTime.parse(date);
      } else {
        return DateTime.parse(date);
      }
    } catch (e) {
      return DateTime.now();
    }
  }

  // Helper method to get display start time
  String get displayStartTime {
    if (task != null) {
      return task!.startTime;
    }
    if (type == 'order') {
      try {
        final dateTime = DateTime.parse(startTime);
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        return startTime;
      }
    }
    return startTime;
  }

  // Helper method to get display end time
  String get displayEndTime {
    if (task != null) {
      return task!.endTime;
    }
    if (type == 'order') {
      try {
        final dateTime = DateTime.parse(endTime);
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        return endTime;
      }
    }
    return endTime;
  }
}

class Task {
  final int id;
  final String startTime;
  final String endTime;

  Task({
    required this.id,
    required this.startTime,
    required this.endTime,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'] ?? 0,
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }
}

enum JobStatus {
  approved,
  absent,
  notStarted,
  ended;

  static JobStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return JobStatus.approved;
      case 'absent':
        return JobStatus.absent;
      case 'not_started':
        return JobStatus.notStarted;
      case 'ended':
        return JobStatus.ended;
      default:
        return JobStatus.notStarted;
    }
  }

  String get displayName {
    switch (this) {
      case JobStatus.approved:
        return 'Approved';
      case JobStatus.absent:
        return 'Absent';
      case JobStatus.notStarted:
        return 'Not Started';
      case JobStatus.ended:
        return 'Ended';
    }
  }
}
