import 'dart:developer';

import 'package:get_clean/app/modules/jobs/models/calendar_job_model.dart';
import 'package:get_clean/app/modules/jobs/models/job_application_model.dart';
import 'package:get_clean/app/modules/jobs/models/job_model.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/constants/constants.dart';

class JobsRemoteProvider {
  final DioHelper helper = DioHelper();

  // Get all jobs
  Future<JobsResponse?> getJobs() async {
    try {
      final response = await helper.getData(getJobsUrl);

      if (response['status'] == 'success') {
        return JobsResponse.fromJson(response);
      } else {
        showErrorToast(response['message'] ?? 'Failed to get jobs');
        return null;
      }
    } catch (e, s) {
      log('Error getting jobs: ${e.toString()} $s');
      return null;
    }
  }

  // Add new job
  Future<bool> addJob(Map<String, dynamic> data) async {
    try {
      final response = await helper.postData(addJobs, data);

      if (response['status'] == 'success') {
        showSuccessToast(response['message'] ?? 'Job added successfully');
        return true;
      } else {
        showErrorToast(response['message'] ?? 'Failed to add job');
        return false;
      }
    } catch (e) {
      log('Error adding job: ${e.toString()}');
      return false;
    }
  }

  // // Edit job
  // Future<bool> editJob(int id, Map<String, dynamic> data) async {
  //   try {
  //     final response = await helper.postData('user/jobs/update/$id', data);
  //
  //     if (response['status'] == 'success') {
  //       showSuccessToast(response['message'] ?? 'Job updated successfully');
  //       return true;
  //     } else {
  //       showErrorToast(response['message'] ?? 'Failed to update job');
  //       return false;
  //     }
  //   } catch (e) {
  //     log('Error updating job: ${e.toString()}');
  //     return false;
  //   }
  // }

  // Delete job
  Future<bool> deleteJob(int id) async {
    try {
      final response = await helper.postData(deleteJobs, {
        'id': id,
      });

      if (response['status'] == 'success') {
        showSuccessToast(response['message'] ?? 'Job deleted successfully');
        return true;
      } else {
        showErrorToast(response['message'] ?? 'Failed to delete job');
        return false;
      }
    } catch (e) {
      log('Error deleting job: ${e.toString()}');
      return false;
    }
  }

  // Get job applications
  Future<JobApplicationsResponse?> getJobApplications() async {
    try {
      final response = await helper.getData('job-applications');

      if (response['success'] == true) {
        return JobApplicationsResponse.fromJson(response);
      } else {
        showErrorToast(response['message'] ?? 'Failed to get job applications');
        return null;
      }
    } catch (e) {
      log('Error getting job applications: ${e.toString()}');
      return null;
    }
  }

  // Get calendar jobs
  Future<CalendarJobsResponse?> getCalendarJobs({
    required int providerId,
    String? day,
    required String month,
  }) async {
    try {
      String url = 'provider/get-works?provider_id=$providerId&month=$month';
      if (day != null) {
        url += '&day=$day';
      }

      final response = await helper.getData(url);

      if (response['success'] == true) {
        return CalendarJobsResponse.fromJson(response);
      } else {
        showErrorToast(response['message'] ?? 'Failed to get calendar jobs');
        return null;
      }
    } catch (e) {
      log('Error getting calendar jobs: ${e.toString()}');
      return null;
    }
  }

  // Update task time
  Future<bool> updateTaskTime({
    required int jobApplicationId,
    required int dId,
    required String executionDate,
    required String time,
    String? endTime,
  }) async {
    try {
      final data = {
        'job_application_id': jobApplicationId,
        'd_id': dId,
        'execution_date': executionDate,
        'time': time,
      };

      if (endTime != null) {
        data['end_time'] = endTime;
      }

      final response = await helper.postData('tasks/update-time', data);

      if (response['success'] == true) {
        showSuccessToast(response['message'] ?? 'Task updated successfully');
        return true;
      } else {
        showErrorToast(response['message'] ?? 'Failed to update task');
        return false;
      }
    } catch (e) {
      log('Error updating task time: ${e.toString()}');
      return false;
    }
  }
}
