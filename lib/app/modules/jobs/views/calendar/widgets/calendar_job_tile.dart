import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/models/calendar_job_model.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class CalendarJobTile extends StatelessWidget {
  final CalendarJob job;
  final VoidCallback? onStartTask;
  final VoidCallback? onEndTask;

  const CalendarJobTile({
    Key? key,
    required this.job,
    this.onStartTask,
    this.onEndTask,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final langController = Get.find<LanguageController>();
    
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with type and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: job.type == 'order' ? Colors.blue.shade100 : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    job.type == 'order' 
                        ? (langController.keys.value.order ?? 'Order')
                        : (langController.keys.value.jobApplication ?? 'Job Application'),
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: job.type == 'order' ? Colors.blue.shade700 : Colors.green.shade700,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: _getStatusColor(job.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusDisplayName(job.status),
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            // Time information
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16.sp,
                  color: primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  '${job.displayStartTime} - ${job.displayEndTime}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            // Job ID information
            if (job.orderId != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    size: 16.sp,
                    color: Colors.grey.shade600,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '${langController.keys.value.orderNumber ?? 'Order'} #${job.orderId}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
            
            if (job.jobApplicationId != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.work,
                    size: 16.sp,
                    color: Colors.grey.shade600,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '${langController.keys.value.jobApplication ?? 'Job Application'} #${job.jobApplicationId}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
            
            // Action buttons for job applications
            if (job.type == 'job_application') ...[
              SizedBox(height: 12.h),
              Row(
                children: [
                  if (job.canStart) ...[
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onStartTask,
                        icon: Icon(Icons.play_arrow, size: 16.sp),
                        label: Text(
                          langController.keys.value.start ?? 'Start',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (job.canEnd) ...[
                    if (job.canStart) SizedBox(width: 8.w),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onEndTask,
                        icon: Icon(Icons.stop, size: 16.sp),
                        label: Text(
                          langController.keys.value.end ?? 'End',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'absent':
        return Colors.red;
      case 'not_started':
        return Colors.orange;
      case 'ended':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    final langController = Get.find<LanguageController>();
    
    switch (status.toLowerCase()) {
      case 'approved':
        return langController.keys.value.approved ?? 'Approved';
      case 'absent':
        return langController.keys.value.absent ?? 'Absent';
      case 'not_started':
        return langController.keys.value.notStarted ?? 'Not Started';
      case 'ended':
        return langController.keys.value.ended ?? 'Ended';
      default:
        return status;
    }
  }
}
