import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/service_providers/controllers/states/providers_state.dart';
import 'package:get_clean/app/modules/service_providers/provider/providers_remote_provider.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/models/providers.dart';

import '../../../../global/models/areas.dart';
import '../../../../global/models/cities.dart';
import '../../../../global/models/city.dart';
import '../../../../global/remote_data_source/remote_data_source.dart';
import '../../../routes/app_pages.dart';
import '../views/widgets/aurgent_bottom_sheet.dart';
import '../views/widgets/service_provider_filter_bottom_sheet.dart';

class ServiceProvidersController extends GetxController {
  final service = Get.arguments != null ? Get.arguments['service'] : null;

  // final isMeter =
  //     Get.arguments != null ? (Get.arguments['isMeter'] ?? false) : false;
  // final isHour =
  //     Get.arguments != null ? (Get.arguments['isHour'] ?? false) : false;

  bool get isHour => serviceFromGlobal.value.pricingOption?.id == 1;

  bool get isMeter => serviceFromGlobal.value.pricingOption?.id == 2;

  final remoteProvider = ProvidersRemoteProvider();
  final providers = Providers().obs;
  final providersState = ProvidersState().obs;

  // final serviceFromGlobal = ProviderServices().obs;

  Rx<ProviderServices> get serviceFromGlobal =>
      Rx<ProviderServices>(service.pricingOption?.id == null
          ? (service.service ?? ProviderServices())
          : service);

  // filter bottom sheet data
  final selectedDate = DateTime.now().obs;
  final howManyHours = 2.obs;
  final pickedTime =
      '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}'
          .obs;

  final filtered = false.obs;

  // aurgent bottom sheet
  final iNeedMaterial = false.obs;

  TextEditingController address = TextEditingController();
  TextEditingController note = TextEditingController();
  TextEditingController howManyMeters = TextEditingController();

  @override
  void onInit() async {
    super.onInit();
    initData();
  }

  Future<void> initData() async {
    providersState.value = ProvidersLoadingState();
    update();

    areas.value = await getAreas();
    update();

    if (service != null) {
      // log('asfsafsafsa ${service}');
      // serviceFromGlobal.value =
      //     Get.find<GlobalValuesController>().allServices.value.data!.firstWhere(
      //           (element) => service.id == element.id,
      //           // orElse: () => ProviderServices(),
      //         );

      await getProviders({
        'service_id': service.id,
        if (isLoggedIn()) 'user_id': currentUserId()
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    filtered.value = false;
    selectedDate.value = DateTime.now();
    pickedTime.value =
        '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}';

    // cities and areas
    cities.value = Cities();
    areas.value = Areas();
    choosedArea.value = Area();
    choosedCity.value = City();
  }

  ///////////////////////////////////////////////////////////////////////
  ///                   Cities And Areas Section                      ///
  ///////////////////////////////////////////////////////////////////////

  // cities and areas provider
  final citiesAndAreasRemoteProvider = CitiesAndAreasRemoteDataSource();

  // cities and areas
  final cities = Cities().obs;
  final areas = Areas().obs;

  final choosedArea = Area().obs;
  final choosedCity = City().obs;

  Future<void> onChangeArea(value) async {
    choosedCity.value = City();
    cities.value = await getCities(value.id!);
    choosedArea.value = value;

    choosedCity.value = cities.value.data?.firstOrNull ?? City();

    update();
  }

  void onChangeCity(value) {
    choosedCity.value = value;
    update();
  }

  Future<Cities> getCities(int areaId) async {
    final response = await citiesAndAreasRemoteProvider.getAllCities(areaId);

    if (response != null) {
      return Cities.fromJson(response);
    }
    return Cities();
  }

  Future<Areas> getAreas() async {
    final response = await citiesAndAreasRemoteProvider.getAllAreas();

    if (response != null) {
      return Areas.fromJson(response);
    }
    return Areas();
  }

  ///////////////////////////////////////////////////////////////////////
  ///              End Of Cities And Areas Section                    ///
  ///////////////////////////////////////////////////////////////////////

  Future<void> getProviders(params) async {
    providersState.value = ProvidersLoadingState();
    update();
    final mainServiceId = serviceFromGlobal.value.pricingOption?.id;

    providersState.value = await remoteProvider.getProviders(
      params,
      isOffer: mainServiceId == 5,
    );

    if (providersState.value is ProvidersSuccessState) {
      providers.value = providersState.value.providers!;
    }
    update();
  }

  void changeSelectedDate(DateTime date) {
    selectedDate.value = date;
    update();
  }

  void showFilterBottomSheet() {
    Get.bottomSheet(
      isScrollControlled: true,
      const ServiceProviderFilterBottomSheet(),
    );
  }

  void showAurgentBottomSheet() {
    Get.bottomSheet(
      isScrollControlled: true,
      const AurgentBottomSheet(),
    );
  }

  void onSelectedHoursTap(int hours) {
    howManyHours.value = hours;
    update();
  }

  void showTimePickers(BuildContext context) async {
    final time =
        await showTimePicker(context: context, initialTime: TimeOfDay.now());
    pickedTime.value =
        '${time!.hour.toString().length == 1 ? '0${time.hour}' : time.hour.toString()}:${time.minute.toString().length == 1 ? '0${time.minute}' : time.minute.toString()} ${time.hour > 12 ? 'PM' : 'AM'}';
    update();
  }

  void filterServices() async {
    log('Areaa ${choosedArea.value.id}');

    if (serviceFromGlobal.value.pricingOption!.id == 3) {
      for (var element in serviceFromGlobal.value.typesSettings!) {
        howManyHours.value += int.tryParse(element.hours.toString()) ?? 0;
      }

      howManyHours.value =
          howManyHours.value ~/ serviceFromGlobal.value.typesSettings!.length;
    }

    if (serviceFromGlobal.value.pricingOption!.id == 2) {
      howManyHours.value = int.tryParse(howManyMeters.text) ?? 1;
    }

    getProviders(
      {
        if (isLoggedIn()) 'user_id': currentUserId(),
        "service_id": service.id,
        "city_id": choosedCity.value.id,
        // "address_id": choosedCity.value.id,
        "date":
            '${selectedDate.value.year}-${selectedDate.value.month.toString().length == 1 ? '0${selectedDate.value.month}' : selectedDate.value.month}-${selectedDate.value.day.toString().length == 1 ? '0${selectedDate.value.day}' : selectedDate.value.day}',
        "time": pickedTime.value.replaceRange(
          pickedTime.value.length - 2,
          pickedTime.value.length,
          '',
        ),
      },
    );
    Get.back();
    filtered.value = true;
    update();
  }

  void onAurgentPressed() async {
    providersState.value = AurgentRequestLoadingState();
    update();

    if (serviceFromGlobal.value.pricingOption?.id == 3) {
      for (var element in serviceFromGlobal.value.typesSettings!) {
        howManyHours.value += (int.tryParse(element.hours.toString()) ?? 1) *
            element.estimatedDuration!;
      }

      howManyHours.value =
          howManyHours.value ~/ serviceFromGlobal.value.typesSettings!.length;
    }

    if (serviceFromGlobal.value.pricingOption!.id == 2) {
      howManyHours.value = int.tryParse(howManyMeters.text) ?? 1;
    }

    bool res = false;
    // if (serviceFromGlobal.value.pricingOption!.id == 3) {

    if (isSofaService(
      serviceFromGlobal.value.pricingOption!.id,
    )) {
      res = await remoteProvider.sendAurgentRequest(
        {
          // "provider_id": Get.find<HomeController>().user.value.id,
          "address": address.text,
          "need_materials": iNeedMaterial.value,
          "note": note.text,
          "service_id": service.id,
          "city_id": choosedArea.value.id,
          "duration": howManyHours.value,
          "quantity[]": howManyHours.value,
          "date[]":
              '${DateTime.now().year}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month}' : DateTime.now().month}-${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day}' : DateTime.now().day}',
          "time[]": pickedTime.value.replaceRange(
            pickedTime.value.length - 2,
            pickedTime.value.length,
            '',
          ),
          "pricing_types": serviceFromGlobal.value.pricingOption!.optionTypes!
              .map((e) => e.id)
              .toList()
              .join(','),
          "pricing_option": serviceFromGlobal.value.typesSettings!
              .map((e) => e.hours)
              .toList()
              .join(','),
        },
      );
    } else if (isMeterService(serviceFromGlobal.value.pricingOption?.id)) {
      res = await remoteProvider.sendAurgentRequest(
        {
          // "provider_id": Get.find<HomeController>().user.value.id,
          "address": address.text,
          "need_materials": iNeedMaterial.value,
          "note": note.text,
          "service_id": service.id,
          "city_id": choosedArea.value.id,
          "duration": howManyHours.value,
          "quantity[]": howManyHours.value,
          "pricing_option": howManyHours.value,
          "date[]":
              '${DateTime.now().year}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month}' : DateTime.now().month}-${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day}' : DateTime.now().day}',
          "time[]": pickedTime.value.replaceRange(
            pickedTime.value.length - 2,
            pickedTime.value.length,
            '',
          ),
        },
      );
    } else {
      res = await remoteProvider.sendAurgentRequest(
        {
          // "provider_id": Get.find<HomeController>().user.value.id,
          "address": address.text,
          "need_materials": iNeedMaterial.value,
          "note": note.text,
          "service_id": service.id,
          "city_id": choosedArea.value.id,
          "duration": howManyHours.value,
          "quantity[]": howManyHours.value,
          "pricing_option": howManyHours.value,
          "date[]":
              '${DateTime.now().year}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month}' : DateTime.now().month}-${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day}' : DateTime.now().day}',
          "time[]": pickedTime.value.replaceRange(
            pickedTime.value.length - 2,
            pickedTime.value.length,
            '',
          ),
        },
      );
    }

    providersState.value = AurgentRequestSuccessState();
    update();

    if (res) {
      Get.back();
    }

    update();
  }

  void onSubmitSearch(value) {
    getProviders(
      {
        if (isLoggedIn()) 'user_id': currentUserId(),
        "search": value,
        "service_id": service.id,
      },
    );
  }

  void onChangeNeedMaterial(value) {
    iNeedMaterial.value = value;
    update();
  }

  void onProviderPressed(
    Provider provider,
  ) {
    log('siddddi ${service.id!}');

    log('siddGFFFFFddi ${provider.services!.firstWhereOrNull(
          (element) => element.service?.id == service.id,
        )?.toJson()}');

    Get.toNamed(Routes.PROVIDER_PAGE, arguments: {
      'provider': provider,

      // providers.value.data![index],
      'service': provider.services!.firstWhereOrNull(
        (element) => element.service?.id == service.id,
      )
    });
  }

  // void onProviderPressed(index) {
  //   // filtered.value
  //   //     ? Get.toNamed(Routes.PROVIDER_PAGE_FILTERED, arguments: {
  //   //         'provider': providers.value.data![index],
  //   //         'date': selectedDate.value,
  //   //         'duration': howManyHours.value,
  //   //         'service': Get.find<GlobalValuesController>()
  //   //             .allServices
  //   //             .value
  //   //             .data!
  //   //             .firstWhere(
  //   //               (element) => element.id! == service.id!,
  //   //             ),
  //   //         'typesSettings': serviceFromGlobal.value.typesSettings!,
  //   //       })
  //   //     :
  //
  //   log('siddddi ${service.id!}');
  //   log('fafasfsafafs ${Get.find<GlobalValuesController>().allServices.value.data?.map(
  //         (e) => e.service?.id,
  //       )}');
  //
  //   log('siddFFddDDDDi ${Get.find<GlobalValuesController>().allServices.value.data!.firstWhereOrNull(
  //         (element) => element.id == service.service?.id,
  //       )}');
  //
  //   Get.toNamed(Routes.PROVIDER_PAGE, arguments: {
  //     'provider': providers.value.data![index],
  //     'service': service,
  //     // Get.find<GlobalValuesController>()
  //     //     .allServices
  //     //     .value
  //     //     .data!
  //     //     .firstWhereOrNull(
  //     //       (element) => element.service?.id == service.id!,
  //     //     ),
  //   });
  // }

  void onChangeHowManyHoursSofa(int value, int index) {
    serviceFromGlobal.value.typesSettings![index].hours = value;
    update();
  }
}
