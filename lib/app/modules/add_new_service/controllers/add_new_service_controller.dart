import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/provider/add_new_service_provider.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/car_model.dart';
import 'package:get_clean/global/models/hour_meter_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/models/service_offer_model.dart';
import 'package:get_clean/global/models/sofa_model.dart';

bool isHoursService(int? id) {
  return id == 1;
}

bool isMeterService(int? id) {
  return id == 2;
}

bool isSofaService(int? id) {
  return id == 3;
}

bool isCarService(int? id) {
  return id == 4;
}

bool isOffer(int? id) {
  return id == 5;
}

bool isClothesService(int? id) {
  return id == 6;
}

class AddNewServiceController extends GetxController {
  final choosedService = ProviderServices().obs;
  final allServices = Get.find<GlobalValuesController>().allServices;
  TextEditingController materialPriceController = TextEditingController();
  TextEditingController minHoursController = TextEditingController(text: '2');

  final sofaList = <SofaModel>[].obs;
  final carList = <CarModel>[].obs;
  final hourMeterList = <HourMeterModel>[].obs;

  final offersList = <ServiceOfferData>[].obs;
  final selectedOffers = <ServiceOfferData>[].obs;

  onChangeOffer(ServiceOfferData value) {
    if (selectedOffers.contains(value)) {
      selectedOffers.remove(value);
    } else {
      selectedOffers.add(value);
    }
    update();
  }

  final provider = AddNewServiceProvider();

  void getServiceOffers() async {
    final response = await provider.getOfferServices();
    if (response.success == true) {
      offersList.value = response.data!;
      update();
    }
  }

  @override
  void onInit() {
    super.onInit();

    getServiceOffers();
  }

  final withTax = false.obs;

  void onChangeService(value) {
    choosedService.value = value;

    // if type is sofa and has options we show the sofa widget
    if (choosedService.value.pricingOption!.hasTypes!) {
      sofaList.value = choosedService.value.pricingOption!.optionTypes!
          .map(
            (option) => SofaModel(
                option.id!, TextEditingController(text: option.name!)),
          )
          .toList();

      carList.value = choosedService.value.pricingOption!.optionTypes!
          .map(
            (option) => CarModel(
                option.id!,
                TextEditingController(text: option.name!),
                option.carServices ?? []),
          )
          .toList();

      update();
    }

    update();
  }

  void addNewHourMeterRow() {
    hourMeterList.add(HourMeterModel(
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
    ));
    update();
  }

  void removeHourMeterRow(HourMeterModel model) {
    hourMeterList.remove(model);
    update();
  }

  void onChangeWithTax(value) {
    withTax.value = value;
    update();
  }

  var isDeliver = false.obs;

  onDeliverChange(value) {
    isDeliver.value = value;

    update();
  }

  void addNewService() async {
    showWaitingIndicator();
    // if service is sofa or generally has options
    if (choosedService.value.pricingOption!.hasTypes!) {
      if (isCarService(choosedService.value.pricingOption?.id)) {
        await provider.addNewServiceWithTypes({
          "service_id": choosedService.value.id,
          "type_id": 4,
          "material_price": 0,
          "price": 1,
          "with_tax": withTax,
          for (var element in carList)
            "subService[${element.id}]":
                jsonEncode(element.carServices.fold<Map<String, dynamic>>(
              {},
              (map, e) {
                map["${e.id}"] = e.price.text.isEmpty ? "0" : e.price.text;
                return map;
              },
            )),
        });
      } else {
        await provider.addNewServiceWithTypes({
          "service_id": choosedService.value.id,
          "material_price": materialPriceController.text.isEmpty
              ? '0'
              : materialPriceController.text,
          // "type_id": sofaList.map((element) => element.id).toList().join(','),
          // "price": sofaList
          //     .map((element) => double.tryParse(element.price.text) ?? 0)
          //     .toList()
          //     .join(','),
          for (var element in sofaList)
            "sub_category[${element.id}]": element.id,

          for (var element in sofaList)
            "price[${element.id}]":
                element.price.text.isEmpty ? '0' : element.price.text,
          "with_tax": withTax,
          "deliver": isDeliver.value
        });
      }
    } else {
      if (isOffer(choosedService.value.pricingOption?.id)) {
        final data = {
          for (var element in selectedOffers) "service_ids[]": element.id,
        };

        await provider.addOfferServices(
          data,
        );
      } else {
        await provider.addNewServiceWithoutType({
          "service_id": choosedService.value.id,
          "material_price": materialPriceController.text.isEmpty
              ? "0"
              : materialPriceController.text,
          // "from": hourMeterList
          //     .map((element) => double.parse(element.from.text))
          //     .toList()
          //     .join(','),
          for (var element in hourMeterList)
            "from[]": double.tryParse(element.from.text) ?? 0,
          // "to": hourMeterList
          //     .map((element) => double.parse(element.to.text))
          //     .toList()
          //     .join(','),
          for (var element in hourMeterList)
            "to[]": double.tryParse(element.to.text) ?? 0,
          // "price": hourMeterList
          //     .map((element) => double.parse(element.price.text))
          //     .toList()
          //     .join(','),
          for (var element in hourMeterList)
            "price[]": double.tryParse(element.price.text) ?? 0,
          "with_tax": withTax,
          "min_hours": minHoursController.text,
        });
      }
    }

    hideWaitingIndicator();

    Get.back();
  }
}
