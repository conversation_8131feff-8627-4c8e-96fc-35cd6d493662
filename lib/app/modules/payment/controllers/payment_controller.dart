import 'dart:developer';

import 'package:get/get.dart';

import '../../../../global/models/user_booking.dart';
import '../../../routes/app_pages.dart';
import '../provider/payment_remote_provider.dart';
import 'states/payment_states.dart';

class PaymentController extends GetxController {
  final BookingData order = Get.arguments['order'];
  final bool payDeposit = Get.arguments['payDeposit'];

  final provider = PaymentRemoteProvider();
  final state = PaymentState().obs;

  final paidTips = 0.0.obs;

  // void addTipAsUser() {
  //   final tipController = TextEditingController();
  //   final key = GlobalKey<FormState>();
  //   Get.dialog(
  //     Scaffold(
  //       backgroundColor: Colors.transparent,
  //       body: Form(
  //         key: key,
  //         child: Center(
  //           child: Container(
  //             padding: const EdgeInsets.all(10),
  //             height: Get.height * 0.3,
  //             width: Get.width * 0.9,
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(10),
  //               color: Colors.white,
  //             ),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Container(
  //                   alignment: Alignment.centerRight,
  //                   child: TextButton(
  //                     onPressed: Get.back,
  //                     child: const Icon(Icons.clear),
  //                   ),
  //                 ),
  //                 Text(
  //                   Get.find<LanguageController>().keys.value.addTip!,
  //                   style: big2TextStyle,
  //                 ),
  //                 CustomFormField(
  //                   keyboardType: TextInputType.number,
  //                   controller: tipController,
  //                   hint: Get.find<LanguageController>().keys.value.amount!,
  //                   validator: (value) {
  //                     if (value.isEmpty) {
  //                       return 'Please Insert The Tip';
  //                     }
  //                     return null;
  //                   },
  //                 ),
  //                 CustomButton(
  //                   label: Get.find<LanguageController>().keys.value.submit!,
  //                   onTap: () async {
  //                     if (key.currentState!.validate()) {
  //                       Get.back();
  //                       state.value = PaymentLoadingState();
  //                       update();
  //
  //                       state.value = await provider.addTip(
  //                         order.id!.toInt(),
  //                         num.parse(tipController.text),
  //                       );
  //
  //                       if (state.value is PaymentSuccessState) {
  //                         paidTips.value = double.parse(tipController.text);
  //                       }
  //                       update();
  //                     }
  //                   },
  //                   height: 50.h,
  //                   width: 200.w,
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //     barrierDismissible: true,
  //   );
  // }

  void confirmPayment() async {
    state.value = PaymentLoadingState();
    update();
    if (payDeposit) {
      state.value = await provider.approveOrderAsUser(order.id!.toInt());
    } else {
      state.value = await provider.payRestOfTotalPrice(order.id!.toInt());
    }

    update();

    log('PaymentLink -> ${state.value.paymentLink}');

    if (state.value.paymentLink != null) {
      // await launchUrl(Uri.parse(state.value.paymentLink!));
      Get.toNamed(
        Routes.PAYMENT_WEBVIEW,
        arguments: {
          'url': state.value.paymentLink!,
          'order': order,
          'tip': paidTips.value,
          'payDeposit': payDeposit,
        },
      );
    }
  }
}
