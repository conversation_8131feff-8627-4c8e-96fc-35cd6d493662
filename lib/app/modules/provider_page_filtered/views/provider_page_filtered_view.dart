import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/multi_widget/main_multi_how_many_hours_widget.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/states/provider_page_filtered_states.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/car_service_widget.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/choose_time.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/map_location_picker.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/order_clothes_widget.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/order_sofa_widget.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/price_section.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:get_clean/global/widget/main_date_widget.dart';
import 'package:get_clean/global/widget/text_with_background.dart';
import 'package:intl/intl.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../provider_page/controllers/provider_page_controller.dart';
import '../../service_providers/controllers/service_providers_controller.dart';

class ProviderPageFilteredView extends GetView<ProviderPageFilteredController> {
  const ProviderPageFilteredView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get.find<ProviderPageController>()
    Get.put(ProviderPageController());
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      if (controller.state.value is ProviderPageFilteredLoadingState) {
        return const LoadingWidget();
      }

      final choosedArea =
          Get.find<ServiceProvidersController>().choosedArea.value.name ??
              Get.find<ProviderPageController>().choosedArea.value.name;
      final choosedCity =
          Get.find<ServiceProvidersController>().choosedCity.value.name ??
              Get.find<ProviderPageController>().choosedCity.value.name;

      final providerClothService =
          controller.provider.services?.firstWhereOrNull(
        (element) => element.pricingOption!.id == 6,
      );

      return Form(
        key: controller.formKey,
        child: Scaffold(
          backgroundColor: Colors.white,

          // price section
          bottomSheet: const PriceSection(),
          body: Container(
            width: Get.width,
            height: Get.height,
            padding: EdgeInsets.only(
              top: 10,
              bottom: 60.h,
              left: 10,
              right: 10,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
                opacity: 0.3,
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // app bar
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        IconButton(
                          onPressed: Get.back,
                          icon: const Icon(
                            CupertinoIcons.back,
                            size: 30,
                          ),
                        ),
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.white,
                          backgroundImage:
                              NetworkImage(controller.provider.image!),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            controller.provider.name ?? 'Provider',
                            style: bigTextStyle,
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 20.h),

                    // address container
                    Row(
                      children: [
                        TextWithBackground(
                          color: Colors.white,
                          text: '$choosedArea , $choosedCity',
                          textColor: primaryColor,
                          fontSize: 14.sp,
                        ),
                      ],
                    ),

                    // address form field
                    CustomFormField(
                      keyboardType: TextInputType.text,
                      controller: controller.addressController,
                      label: Get.find<LanguageController>().keys.value.address!,
                      validator: (value) {
                        if (value.isEmpty) {
                          return Get.find<LanguageController>()
                              .keys
                              .value
                              .pleaseEnterYourAddress!;
                        }
                        return null;
                      },
                    ),

                    Center(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  MapLocationPicker(
                                selectedMarkers: controller.marker,
                                onSave: (marker) {
                                  controller.setMarker(marker);
                                },
                              ),
                            ),
                          );
                        },

                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .setLocationInMap!,
                              style: const TextStyle(
                                  color: bgColor,
                                  decoration: TextDecoration.underline,
                                  decorationColor: bgColor),
                            ),
                            if (controller.marker.isNotEmpty)
                              const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.0),
                                child: Icon(Icons.check_circle,
                                    color: primaryColor),
                              ),
                          ],
                        ),

                        // child: Text(S.of(context).SetLocationOnMap),
                      ),
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    // * Multi Hours Widget =================
                    if (controller.isHoursAndMulti)
                      MainMultiHoursWidget(
                          isInitExpanded: false,
                          additionalOnChanged: () {
                            controller.requestPrice();
                          })
                    else
                      const _Body(),

                    CustomFormField(
                      keyboardType: TextInputType.text,
                      controller: controller.notesController,
                      label: Get.find<LanguageController>().keys.value.note!,
                      maxLines: 5,
                      minLines: 3,
                    ),

                    if (!isCarService(
                            controller.service.service?.pricingOption?.id) &&
                        !isClothesService(
                            controller.service.service?.pricingOption?.id))
                      Row(
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: controller.iNeedMaterials.value,
                                onChanged: controller.onChangeINeedMaterials,
                              ),
                              SizedBox(width: 5.w),
                              Text(
                                Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .iNeedMaterial!,
                                style: big2TextStyle,
                              ),
                            ],
                          ),
                        ],
                      ),

                    if (isClothesService(
                            controller.service.service?.pricingOption?.id) &&
                        providerClothService?.deliver == true)
                      Row(
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: controller.iNeedMaterials.value,
                                onChanged: controller.onChangeINeedMaterials,
                              ),
                              SizedBox(width: 5.w),
                              Text(
                                Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .deliver!,
                                style: big2TextStyle,
                              ),
                            ],
                          ),
                        ],
                      ),

                    if (Get.find<GlobalValuesController>().isLoggedIn.value)
                      Center(
                        child: controller.state.value
                                is ProviderPageFilteredLoadingState
                            ? const CircularProgressIndicator()
                            : CustomButton(
                                label: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .submit!,
                                onTap: controller.onSubmitButtonPressed,
                                height: 41.h,
                                width: 190.w,
                              ),
                      ),

                    KeyboardVisibilityBuilder(
                        builder: (context, isKeyboardVisible) {
                      return SizedBox(height: isKeyboardVisible ? 200.h : 30.h);
                    }),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}

class _Body extends GetView<ProviderPageFilteredController> {
  const _Body({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Get.find<LanguageController>().isArabic
              ? Alignment.centerRight
              : Alignment.centerLeft,
          child: Text(
            Get.find<LanguageController>().keys.value.selectedDay!,
            style: big2TextStyle,
          ),
        ),
        SizedBox(
          height: 100,
          child: ListView.builder(
            controller: controller.scrollController,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index));

              return Obx(() => InkWell(
                    onTap: () => controller.changeSelectedDate(date),
                    child: Container(
                      width: 120,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        border: Border.all(color: primaryColor),
                        borderRadius: BorderRadius.circular(10),
                        color: controller.selectedDate.value.day == date.day &&
                                controller.selectedDate.value.month ==
                                    date.month
                            ? primaryColor
                            : Colors.white,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            DateFormat.d().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                          ),
                          Text(
                            DateFormat.EEEE().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                          ),
                        ],
                      ),
                    ),
                  ));
            },
            itemCount: 60,
          ),
        ),
        Obx(
          () => MainDateWidget(
            date:
                DateFormat('dd/MM/yyyy').format(controller.selectedDate.value),
          ),
        ),

        SizedBox(height: 20.h),

        // if service has types that means it's sofa so we show sofa
        if ((controller.service.service?.pricingOption?.hasTypes ?? false) &&
            !isCarService(controller.service.service?.pricingOption!.id!) &&
            !isClothesService(controller.service.service?.pricingOption!.id!))
          const OrderSofaWidget(),

        if (isClothesService(controller.service.service?.pricingOption?.id))
          const OrdersHowManyClothes(),

        // if service not has types then it's normal service
        if (!(controller.service.service?.pricingOption!.hasTypes ?? false))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 20.h),
              if (controller.howManyTimes.value == SelectedTimes.once)
                Column(
                  children: [
                    if (controller.service.service?.pricingOption!.id == 1)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Get.find<LanguageController>()
                                .keys
                                .value
                                .howManyHours!,
                            style: big2TextStyle,
                          ),
                          Center(
                            child: Text(
                              controller.howManyHours.value.toString() +
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .hour!,
                              style: big2TextStyle,
                            ),
                          ),
                        ],
                      ),
                    if (controller.service.service?.pricingOption!.id == 2)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Get.find<LanguageController>()
                                .keys
                                .value
                                .howManyMeters!,
                            style: big2TextStyle,
                          ),
                          Center(
                            child: Text(
                              controller.howManyMetersController.text
                                      .toString() +
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .meter!,
                              style: big2TextStyle,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
            ],
          ),

        Text(
          Get.find<LanguageController>().keys.value.whatTime!,
          style: big2TextStyle,
        ),

        const ChooseTime(),

        const Divider(color: primaryColor),

        if (isCarService(controller.service.service?.pricingOption?.id))
          const CarServiceWidget(),
      ],
    );
  }
}

//? edit cloth quantity
//? Cart, Two tabs -> (Need Action - TODO) -> Provider (My Booking - My Orders)
