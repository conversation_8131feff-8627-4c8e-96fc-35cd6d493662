import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/models/car_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/models/service_offer_model.dart';

import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/help_functions/help_functions.dart';
import '../../../../global/models/hour_meter_model.dart';
import '../../../../global/models/sofa_model.dart';
import '../../add_new_service/controllers/add_new_service_controller.dart';
import '../provider/edit_service_provider.dart';

class EditServiceController extends GetxController {
  final choosedService = ProviderServices().obs;

  final ProviderServices service = Get.arguments['service'];
  final allServices = Get.find<GlobalValuesController>().allServices;
  TextEditingController materialPriceController = TextEditingController();
  TextEditingController minHoursController = TextEditingController();

  final sofaList = <SofaModel>[].obs;
  final carList = <CarModel>[].obs;
  final hourMeterList = <HourMeterModel>[].obs;

  final offersList = <ServiceOfferData>[].obs;
  final selectedOffers = <ServiceOfferData>[].obs;

  final withTax = false.obs;

  final provider = EditServiceProvider();
  var isDeliver = false.obs;

  @override
  void onInit() {
    super.onInit();
    materialPriceController.text = service.materialPrice!.toString();
    if (service.service!.pricingOption!.hasTypes!) {
      final globalController = Get.find<GlobalValuesController>();
      final allServices = globalController.allServices.value.data; //todo-fix

      // set choosedService
      choosedService.value = allServices!.firstWhereOrNull(
              (element) => element.id == service.service!.id) ??
          allServices.first;

      final priceMap = service.pricingList!
          .asMap()
          .map((_, e) => MapEntry(e.typeModel!.id!, e.price.toString()));

      sofaList.value = choosedService.value.pricingOption!.optionTypes!
          .map(
            (option) => SofaModel.withPrice(
              option.id!,
              TextEditingController(text: option.name!),
              TextEditingController(text: priceMap[option.id!] ?? '0'),
            ),
          )
          .toList();

      carList.value = service.pricingList!
          .map(
            (option) => CarModel(
                option.typeModel!.id!,
                option.typeModel!.name, //fixxxxx
                option.typeModel?.carServices ?? []),
          )
          .toList();
    } else {
      hourMeterList.value = service.pricingList!
          .map(
            (e) => HourMeterModel(
              TextEditingController(text: e.from.toString()),
              TextEditingController(text: e.to.toString()),
              TextEditingController(text: e.price.toString()),
            ),
          )
          .toList();

      minHoursController.text = service.minHours?.toString() ?? '';
    }
    isDeliver.value = service.deliver ?? false;

    withTax.value = service.withTax!;

    getServiceOffers();
    update();
  }

  void getServiceOffers() async {
    final response = await provider.getOfferServices();
    if (response.success == true) {
      offersList.value = response.data!;

      selectedOffers.value = offersList
          .where((element) => element.subscribeStatus == true)
          .toList();

      update();
    }
  }

  void addNewHourMeterRow() {
    hourMeterList.add(HourMeterModel(
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
    ));
    update();
  }

  void removeHourMeterRow(HourMeterModel model) {
    hourMeterList.remove(model);
    update();
  }

  void onChangeWithTax(value) {
    withTax.value = value;
    update();
  }

  onDeliverChange(value) {
    isDeliver.value = value;

    update();
  }

  Future<void> editService() async {
    showWaitingIndicator();
    if (service.service!.pricingOption!.hasTypes!) {
      if (isCarService(service.service!.pricingOption?.id)) {
        await provider.editServiceWithTypes({
          "service_id": service.service?.id,
          // service.service!.id,
          "sub_category[]": 4, //TODO-Check-sub_category

          "material_price": 0,
          "price": 1,
          "with_tax": withTax,
          for (var element in carList)
            "subService[${element.id}]":
                jsonEncode(element.carServices.fold<Map<String, dynamic>>(
              {},
              (map, e) {
                map["${e.id}"] = e.price.text.isEmpty ? "0" : e.price.text;
                return map;
              },
            )),
        }, service.id!);
      } else {
        await provider.editServiceWithTypes({
          "service_id": service.service?.id,
          // service.service!.id,
          "material_price": materialPriceController.text.isEmpty
              ? "0"
              : materialPriceController.text,
          // "type_id": sofaList.map((element) => element.id).toList().join(','),
          // "price": sofaList
          //     .map((element) => double.parse(
          //         element.price.text.isEmpty ? '0.0' : element.price.text))
          //     .toList()
          //     .join(','),

          for (var element in sofaList)
            "sub_category[${element.id}]": element.id,
          for (var element in sofaList)
            "price[${element.id}]":
                element.price.text.isEmpty ? '0' : element.price.text,
          "deliver": isDeliver.value,
          // "with_tax": withTax,
        }, service.id!);
      }
    } else {
      if (isOffer(service.service!.pricingOption?.id)) {
        final data = {
          for (var element in selectedOffers) "service_ids[]": element.id,
        };

        log('OFFERDATA ${data}');
        await provider.addOfferServices(
          data,
        );
      } else {
        await provider.editServiceWithoutType(
          {
            "service_id": service.service?.id,
            // service.service!.id,
            "material_price": materialPriceController.text,
            // "from": hourMeterList
            //     .map((element) => double.parse(element.from.text))
            //     .toList()
            //     .join(','),
            // for (var element in hourMeterList) "from[]": element.from.text,
            for (int i = 0; i < hourMeterList.length; i++)
              "from[$i]": hourMeterList[i].from.text,
            // "to": hourMeterList
            //     .map((element) => double.parse(element.to.text))
            //     .toList()
            //     .join(','),
            for (int i = 0; i < hourMeterList.length; i++)
              "to[$i]": hourMeterList[i].to.text,
            // "price": hourMeterList
            //     .map((element) => double.parse(
            //         element.price.text.isEmpty ? '0.0' : element.price.text))
            //     .toList()
            //     .join(','),
            for (int i = 0; i < hourMeterList.length; i++)
              "price[$i]": hourMeterList[i].price.text.isEmpty
                  ? '0'
                  : hourMeterList[i].price.text,
            // "with_tax": withTax,
            "min_hours": minHoursController.text,
          },
          service.id!,
        );
      }
    }

    hideWaitingIndicator();
  }
}
