import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/company_profile_widget.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/date_time_picker_widget.dart';
import 'package:get_clean/global/widget/provider_profile_widget.dart';
import 'package:get_clean/global/widget/user_profile_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/enums/user_type.dart';
import '../../../../global/widget/custom_button.dart';
import '../../enter_phone_number/controllers/enter_phone_number_controller.dart';
import '../controllers/my_profile_controller.dart';

class MyProfileView extends GetView<MyProfileController> {
  const MyProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(() => MyProfileController());
    return GetBuilder<MyProfileController>(builder: (controller) {
      return Scaffold(
        backgroundColor: primaryColor,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          elevation: 0,
        ),
        body: Stack(
          fit: StackFit.loose,
          children: [
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: Get.height * 0.8,
                width: Get.width,
                padding: EdgeInsets.only(
                  top: 50.h,
                  right: 10,
                  left: 10,
                  bottom: 10,
                ),
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(45),
                    topRight: Radius.circular(45),
                  ),
                  color: Colors.white,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // if provider show text button go to albums page
                      if (controller.choosedUserType.value == UserType.provider)
                        TextButton(
                          onPressed: () {
                            Get.toNamed(Routes.ALBUMS);
                          },
                          child: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .myAlbums!,
                              // "My Albums",
                              style: regularTextStyle.copyWith(
                                decoration: TextDecoration.underline,
                              )),
                        ).paddingOnly(top: 50),

                      CustomFormField(
                        keyboardType: TextInputType.text,
                        controller: controller.name,
                        label:
                            Get.find<LanguageController>().keys.value.fullName!,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseEnterFullName!;
                          }
                          return null;
                        },
                      ),
                      CustomFormField(
                        keyboardType: TextInputType.emailAddress,
                        controller: controller.email,
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .yourEmail!,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseEnterEmail!;
                          }
                          return null;
                        },
                      ),
                      CustomFormField(
                        keyboardType: TextInputType.phone,
                        controller: controller.phone,
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .yourPhoneNumber!,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseEnterPhone!;
                          }
                          return null;
                        },
                        icon: const Text(mainPhoneCode),
                      ),
                      DateTimePickerWidget(
                        controller: controller.birthDate,
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .yourBirthDate!,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseInsertYourBirthDate!;
                          }
                          return null;
                        },
                      ),
                      CustomFormField(
                        keyboardType: TextInputType.text,
                        label:
                            Get.find<LanguageController>().keys.value.address!,
                        controller: controller.address,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseEnterYourAddress!;
                          }
                          return null;
                        },
                      ),
                      CustomDropDownButton(
                        hint: Get.find<LanguageController>().keys.value.gender!,
                        label:
                            Get.find<LanguageController>().keys.value.gender!,
                        value: controller.gender.value,
                        onChanged: controller.onChangeGender,
                        items: [
                          DropdownMenuItem(
                            value: 'male',
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .male!),
                          ),
                          DropdownMenuItem(
                            value: 'female',
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .female!),
                          ),
                        ],
                        buttonHeight: 50.h,
                        buttonWidth: Get.width,
                      ),
                      CustomDropDownButton(
                        buttonHeight: 50.0.h,
                        buttonWidth: Get.width,
                        hint: Get.find<LanguageController>().keys.value.type!,
                        value: controller.choosedUserType.value,
                        onChanged: controller.onChangeChoosedUserType,
                        label: Get.find<LanguageController>().keys.value.type!,
                        items: [
                          DropdownMenuItem(
                            value: UserType.user,
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .user!),
                          ),
                          DropdownMenuItem(
                            value: UserType.provider,
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .provider!),
                          ),
                          DropdownMenuItem(
                            value: UserType.company,
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .company!),
                          ),
                        ],
                      ),
                      if (controller.choosedUserType.value == UserType.company)
                        const CompanyProfileWidget(),
                      if (controller.choosedUserType.value == UserType.provider)
                        const ProviderProfileWidget(),
                      if (controller.choosedUserType.value == UserType.user)
                        const UserProfileWidget(),
                      CustomButton(
                        label: Get.find<LanguageController>().keys.value.workLocations ?? 'Work Locations',
                        onTap: () => Get.toNamed(Routes.WORK_LOCATION_VIEW),
                        height: 49.h,
                        width: 320.w,
                        color: primaryColor,
                      ),
                      SizedBox(height: 10.h),
                      CustomButton(
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .changePassword!,
                        onTap: controller.onChangePasswordPressed,
                        height: 49.h,
                        width: 320.w,
                        color: Colors.red,
                      ),
                      CustomButton(
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .deleteProfile!,
                        onTap: controller.deleteProfile,
                        height: 49.h,
                        width: 320.w,
                        color: Colors.red,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              top: 0.h,
              right: 100.w,
              left: 100.w,
              child: CircleAvatar(
                backgroundColor: const Color(0xff2B5AA5),
                radius: 60.sp,
                child: CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: 55.sp,
                  backgroundImage: controller.userProfile.value.path.isEmpty
                      ? NetworkImage(
                          controller.homeController.user.value.image ??
                              '', //TODO-API
                        )
                      : FileImage(controller.userProfile.value)
                          as ImageProvider,
                ),
              ),
            ),
            Positioned(
              top: 40.h,
              right: 100.w,
              left: 100.w,
              child: InkWell(
                onTap: controller.onChangeUserProfile,
                child: const Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.black54,
                  size: 50,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
