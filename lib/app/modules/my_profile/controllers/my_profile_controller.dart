import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as d;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/my_profile/provider/my_profile_remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/widget/add_area_and_city_bottom_sheet.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/enums/user_type.dart';
import '../../../../global/models/areas.dart';
import '../../../../global/models/cities.dart';
import '../../../../global/models/city.dart';
import '../../../../global/models/skills.dart';
import '../../../../global/models/work_location_model.dart';
import '../../../../global/models/work_zones.dart';
import '../../../../global/remote_data_source/remote_data_source.dart';
import '../../login/providers/local_provider.dart';

class MyProfileController extends GetxController {
  // global values controller
  final globalValuesController = Get.find<GlobalValuesController>();
  final homeController = Get.find<HomeController>();

  // remote provider
  final myProfileRemoteProvider = MyProfileRemoteProvider();
  final citiesAndAreasRemoteProvider = CitiesAndAreasRemoteDataSource();

  // cities and areas
  final cities = Cities().obs;
  final areas = Areas().obs;

  // global variables for all users types
  TextEditingController name = TextEditingController();
  TextEditingController email = TextEditingController();
  TextEditingController phone = TextEditingController();
  TextEditingController address = TextEditingController();
  TextEditingController birthDate = TextEditingController();

  // user gender
  final gender = 'male'.obs;

  // for only user
  final choosedUserType = UserType.user.obs;

  // Work Locations
  final workLocations = <WorkLocation>[].obs;
  final isLoadingWorkLocations = false.obs;
  final selectedWorkLocation = Rxn<WorkLocation>();
  final workLocationNameController = TextEditingController();

  // Map marker for work location
  final workLocationMarker = <Marker>{}.obs;

  final userProfile = File('').obs;

  // for only company
  TextEditingController companyName = TextEditingController();
  TextEditingController companyPhone = TextEditingController();
  TextEditingController companyIdNumber = TextEditingController();
  TextEditingController companyImageController = TextEditingController();
  final companyImage = File('').obs;

  // for only provider
  TextEditingController providerName = TextEditingController();
  TextEditingController providerPhone = TextEditingController();
  TextEditingController providerIdNumber = TextEditingController();
  TextEditingController providerImageController = TextEditingController();
  final providerImage = File('').obs;

  // susbend
  final isSuspend = false.obs;

  //onSuspendSwitchChanged
  void onSuspendSwitchChanged(bool value) {
    isSuspend.value = value;

    update();
  }

  // city and area
  final choosedArea = Area().obs;
  final choosedCity = City().obs;

  String? convertPhoneCode(String? phone) =>
      (phone?.startsWith('+972') ?? false)
          ? phone.toString().substring(4)
          : phone;

  @override
  void onInit() async {
    super.onInit();

    name = TextEditingController(text: homeController.user.value.name);
    email = TextEditingController(text: homeController.user.value.email);
    phone = TextEditingController(
        text: convertPhoneCode(homeController.user.value.phone));
    address = TextEditingController(text: homeController.user.value.address);
    birthDate =
        TextEditingController(text: homeController.user.value.birthDate);

    choosedUserType.value = homeController.user.value.type!.userType;
    gender.value = homeController.user.value.gender ?? 'male';
    update();

    if (choosedUserType.value == UserType.company) {
      companyName = TextEditingController(
        text: homeController.user.value.provider!.name,
      );
      companyPhone = TextEditingController(
        text: convertPhoneCode(
          homeController.user.value.provider!.phone,
        ),
      );
      companyIdNumber = TextEditingController(
        text: homeController.user.value.provider!.idNumber,
      );
      companyImageController = TextEditingController(
        text: homeController.user.value.provider!.idFile,
      );
    }

    if (choosedUserType.value == UserType.provider) {
      providerName = TextEditingController(
        text: homeController.user.value.provider?.name,
      );
      providerPhone = TextEditingController(
        text: convertPhoneCode(
          homeController.user.value.provider!.phone,
        ),
      );
      providerIdNumber = TextEditingController(
        text: homeController.user.value.provider!.idNumber,
      );
      providerImageController = TextEditingController(
        text: homeController.user.value.provider!.idFile,
      );
    }

    if (choosedUserType.value != UserType.user) {
      setWorkZones();
      setSkills();

      isSuspend.value = homeController.user.value.provider!.suspended == '1';
    }

    // get all areas
    areas.value = Areas.fromJson((await getAreas()).toJson());
    areasForWorkZones.value = Areas.fromJson((await getAreas()).toJson());

    initialCitiesAndAreasProfile();
    update();
  }

  /// * /////////////////////////////////////////////////////////////////
  /// *                  Cities And Areas Section                     ///
  /// * //////////////////////////////////////////////////////////////////

  void initialCitiesAndAreasProfile() async {
    // inital the area choosed in user profile
    await onChangeAreaProfile(
      areas.value.data!.firstWhere(
        (element) => element.id == homeController.user.value.district?.id,
        orElse: () => areas.value.data![0],
      ),
    );

    log('afsafsfa ${homeController.user.value.toJson()}');

    // inital the citiy choosed in user profile
    onChangeCityProfile(
      cities.value.data!.firstWhereOrNull(
          (element) => element.id == homeController.user.value.city!.id),
    );
  }

  Future<void> onChangeAreaProfile(value) async {
    choosedCity.value = City();
    cities.value = await getCities(value.id!);
    choosedArea.value = value;
    update();
  }

  void onChangeCityProfile(value) {
    choosedCity.value = value;
    update();
  }

  Future<Cities> getCities(int cityId) async {
    final response = await citiesAndAreasRemoteProvider.getAllCities(cityId);

    if (response != null) {
      return Cities.fromJson(response);
    }
    return Cities();
  }

  Future<Areas> getAreas() async {
    final response = await citiesAndAreasRemoteProvider.getAllAreas();

    if (response != null) {
      return Areas.fromJson(response);
    }
    return Areas();
  }

  ///////////////////////////////////////////////////////////////////////
  ///              End Of Cities And Areas Section                    ///
  ///////////////////////////////////////////////////////////////////////

  ///////////////////////////////////////////////////////////////////////
  ///                  Begin Of Skills Section                       ///
  ///////////////////////////////////////////////////////////////////////

  // skills
  final choosedSkills = <Skills>[].obs;
  final shownSkills = <Skills>[].obs;
  List<Skills> initialSkills = <Skills>[];

  void setSkills() {
    // shown work zones and skills
    shownSkills.value = globalValuesController.skills.value.data!
        .map((e) => Skills.fromJson(e.toJson()))
        .toList();

    choosedSkills.value = homeController.user.value.provider!.skills != null
        ? homeController.user.value.provider!.skills!
            .map((e) => Skills.fromJson(e.toJson()))
            .toList()
        : [];

    initialSkills = homeController.user.value.provider!.skills != null
        ? homeController.user.value.provider!.skills!
            .map((e) => Skills.fromJson(e.toJson()))
            .toList()
        : [];
  }

  void onChangeSkills(values) {
    choosedSkills.clear();

    for (var element in values) {
      final res = choosedSkills.firstWhere((e) => element.value.id == e.id,
          orElse: () => Skills());
      if (res.id == null) {
        choosedSkills.add(element.value);
      }
    }

    homeController.user.value.provider!.skills =
        choosedSkills.map((e) => Skills.fromJson(e.toJson())).toList();

    log('adaskdmdas ${choosedSkills.length}}');
  }

  ///////////////////////////////////////////////////////////////////////
  ///                   End Of Skills Section                         ///
  ///////////////////////////////////////////////////////////////////////

  //?/////////////////////////////////////////////////////////////////////
  ///!                Begin Of Work Zones Section                      ///
  //?/////////////////////////////////////////////////////////////////////

  // work zones
  final choosedWorkZones = <WorkZones>[].obs;
  final shownWorkZones = <WorkZones>[].obs;
  List<WorkZones> initialWorkZones = <WorkZones>[].obs;

  // cities and areas
  final citiesForWorkZones = Cities().obs;
  final areasForWorkZones = Areas().obs;

  // city and area for bottom sheet
  final choosedAreaForWorkZones = Area().obs;
  final choosedCityForWorkZones = City().obs;

  void onChangeChoosedAreaForWorkZones(value) async {
    choosedCityForWorkZones.value = City();
    citiesForWorkZones.value = await getCities(value.id!);
    choosedAreaForWorkZones.value = value;
    update();
  }

  void onChangeChoosedCityForWorkZones(value) {
    choosedCityForWorkZones.value = value;
    update();
  }

  void setWorkZones() {
    choosedWorkZones.value =
        homeController.user.value.provider!.workZones != null
            ? homeController.user.value.provider!.workZones!
                .map((e) => WorkZones.fromJson(e.toJson()))
                .toList()
            : [];

    initialWorkZones = homeController.user.value.provider!.workZones != null
        ? homeController.user.value.provider!.workZones!
            .map((e) => WorkZones.fromJson(e.toJson()))
            .toList()
        : [];

    shownWorkZones.value = homeController.user.value.provider!.workZones != null
        ? homeController.user.value.provider!.workZones!
            .map((e) => WorkZones.fromJson(e.toJson()))
            .toList()
        : [];
  }

  void onChangeWorkZones(values) {
    choosedWorkZones.clear();
    for (var element in values) {
      choosedWorkZones.add(element.value);
    }

    homeController.user.value.provider!.workZones =
        choosedWorkZones.map((e) => WorkZones.fromJson(e.toJson())).toList();
  }

  void onAddNewWorkZone() {
    final workZone = WorkZones(
      id: choosedCityForWorkZones.value.id,
      name: choosedCityForWorkZones.value.name,
      area: City(
        id: choosedAreaForWorkZones.value.id,
        name: choosedAreaForWorkZones.value.name,
      ),
    );

    choosedWorkZones.add(workZone);
    shownWorkZones.add(workZone);

    log('workZooneLength ${choosedWorkZones.length}}');

    homeController.user.value.provider!.workZones =
        choosedWorkZones.map((e) => WorkZones.fromJson(e.toJson())).toList();

    initialWorkZones = homeController.user.value.provider!.workZones!
        .map((e) => WorkZones.fromJson(e.toJson()))
        .toList();

    choosedAreaForWorkZones.value = Area();
    choosedCityForWorkZones.value = City();

    update();

    Get.back();
    Get.offAndToNamed(Routes.MY_PROFILE);
  }

  //?/////////////////////////////////////////////////////////////////////
  ///!                   End Of Work Zones Section                     ///
  //?/////////////////////////////////////////////////////////////////////

  void onChangeGender(value) {
    gender.value = value;
    update();
  }

  void onChangeChoosedUserType(newType) {
    if (newType == UserType.company || newType == UserType.provider) {
      setSkills();
      setWorkZones();
    }
    choosedUserType.value = newType;
    update();
  }

  void onAddAreaPressed() {
    Get.bottomSheet(
      const AddAreaAndCityBottomSheet(),
    );
  }

  void onEditSchduelPressed() {
    Get.toNamed(Routes.EDIT_SCHDUEL);
  }

  void onChangePasswordPressed() {
    Get.toNamed(Routes.CHANGE_PASSWORD);
  }

  void onChangeUserProfile() async {
    userProfile.value = await pickFile();
    update();
  }

  void onChangeCompanyImage() async {
    companyImage.value = await pickFile();
    companyImageController.text = companyImage.value.path;
    update();
  }

  void onChangeProviderImage() async {
    providerImage.value = await pickFile();
    providerImageController.text = providerImage.value.path;
    update();
  }

  void editData(BuildContext context) async {
    if (choosedUserType.value != UserType.user && choosedSkills.isEmpty) {
      showErrorToast(
        Get.find<LanguageController>().keys.value.pleaseChooseAtLeaseOneSkill ??
            'Please choose at lease one skill',
      );
      return;
    }

    showWaitingIndicator();

    final phoneNumber = '+972${phone.text.replaceAll(' ', '')}';

    if (phoneNumber != homeController.user.value.phone) {
      await verifyPhoneNumber(context);

      hideWaitingIndicator();

      return;
    }

    await updateProfile();

    hideWaitingIndicator();
  }

  Future<void> verifyPhoneNumber(BuildContext context) async {
    FirebaseAuth auth = FirebaseAuth.instance;
    final phoneNumber = '+972${phone.text.replaceAll(' ', '')}';

    log('Phone Number: $phoneNumber');

    updateProfile();

    // await auth.verifyPhoneNumber(//TODO-fix
    //   phoneNumber: phoneNumber,
    //   timeout: const Duration(seconds: 60),
    //   verificationCompleted: (AuthCredential credential) async {
    //     UserCredential result = await auth.signInWithCredential(credential);
    //
    //     User? user = result.user;
    //
    //     if (user != null) {
    //       updateProfile();
    //       log('Phone Authentication Success');
    //     } else {
    //       update();
    //       showErrorToast("Error In Auth Phone");
    //       log("Error");
    //     }
    //   },
    //   verificationFailed: (FirebaseAuthException exception) {
    //     log(exception.message.toString());
    //     showErrorToast("Error In Auth Phone");
    //
    //     update();
    //   },
    //   codeSent: (String verificationId, int? forceResendingToken) async {
    //     update();
    //     await Get.bottomSheet(
    //       isScrollControlled: false,
    //       isDismissible: false,
    //       Container(
    //         padding: const EdgeInsets.all(10),
    //         width: Get.width,
    //         height: Get.height,
    //         decoration: const BoxDecoration(
    //           image: DecorationImage(
    //             image: AssetImage(
    //               'assets/images/main_background.png',
    //             ),
    //             fit: BoxFit.fill,
    //           ),
    //         ),
    //         child: SafeArea(
    //           child: SingleChildScrollView(
    //             child: Column(
    //               mainAxisAlignment: MainAxisAlignment.center,
    //               crossAxisAlignment: CrossAxisAlignment.center,
    //               children: [
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: Text(
    //                     Get.find<LanguageController>()
    //                         .keys
    //                         .value
    //                         .verificationCode!,
    //                     style: bigTextStyle,
    //                   ),
    //                 ),
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: Text(
    //                     Get.find<LanguageController>()
    //                         .keys
    //                         .value
    //                         .pleaseEnterVerificationCode!,
    //                     style: regularTextStyle,
    //                     textAlign: TextAlign.center,
    //                   ),
    //                 ),
    //                 Directionality(
    //                   textDirection: TextDirection.ltr,
    //                   child: Padding(
    //                     padding: const EdgeInsets.all(10.0),
    //                     child: SizedBox(
    //                       width: Get.width * 0.8,
    //                       child: PinCodeTextField(
    //                         mainAxisAlignment: MainAxisAlignment.spaceAround,
    //                         appContext: context,
    //                         length: 6,
    //                         onChanged: onChangeOTP,
    //                         enablePinAutofill: true,
    //                         pinTheme: PinTheme(
    //                           shape: PinCodeFieldShape.box,
    //                           inactiveColor: primaryColor,
    //                           activeColor: primaryColor,
    //                           inactiveFillColor: primaryColor,
    //                           activeFillColor: primaryColor,
    //                           disabledColor: primaryColor,
    //                           selectedFillColor: primaryColor,
    //                           selectedColor: primaryColor,
    //                           borderRadius: BorderRadius.circular(10),
    //                           fieldWidth: 50.w,
    //                           fieldHeight: 50.h,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                 ),
    //                 SizedBox(
    //                   height: 40.h,
    //                 ),
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: CustomButton(
    //                     label:
    //                         Get.find<LanguageController>().keys.value.submit!,
    //                     onTap: () async {
    //                       try {
    //                         final code = codeController.text.trim();
    //
    //                         AuthCredential credential =
    //                             PhoneAuthProvider.credential(
    //                           verificationId: verificationId,
    //                           smsCode: code,
    //                         );
    //
    //                         UserCredential result =
    //                             await auth.signInWithCredential(credential);
    //
    //                         User? user = result.user;
    //
    //                         if (user != null) {
    //                           updateProfile();
    //                           log('Phone Authentication Success');
    //                         } else {
    //                           update();
    //                           showErrorToast("Error In Auth Phone");
    //                         }
    //
    //                         Get.back();
    //                       } catch (e) {
    //                         showErrorToast("Please Enter Correct Code");
    //                         log('Error In Auth Phone $e');
    //                       }
    //                     },
    //                     height: 50.h,
    //                     width: 185.w,
    //                   ),
    //                 ),
    //               ],
    //             ),
    //           ),
    //         ),
    //       ),
    //     );
    //   },
    //   codeAutoRetrievalTimeout: (value) {},
    // );
  }

  final codeController = TextEditingController();

  void onChangeOTP(value) {
    codeController.text = value;
    update();
  }

  Future<void> updateProfile() async {
    final phoneNumber = '+972${phone.text.replaceAll(' ', '')}';

    Map<String, dynamic> data = {};
    data['name'] = name.text;
    data['email'] = email.text;
    data['phone'] = phoneNumber;

    data['city_id'] = choosedCity.value.id;
    data['district_id'] = choosedArea.value.id;
    data['address'] = address.text;
    data['birth_date'] = birthDate.text;
    data['gender'] = gender.value;

    if (choosedUserType.value == UserType.provider) {
      Get.find<HomeController>().changeUserType('provider');
      data['provider_name'] = providerName.text;
      data['provider_city_id'] = choosedArea.value.id;
      data['provider_district_id'] = choosedCity.value.id;
      data['provider_phone'] = providerPhone.text.startsWith('0')
          ? providerPhone.text.replaceFirst('0', '+972')
          : providerPhone.text;
      if (providerImage.value.path.isNotEmpty) {
        data['provider_id_file'] = await d.MultipartFile.fromFile(
          providerImage.value.path,
          filename: 'providerImage.png',
        );
      }

      data['provider_id_number'] = providerIdNumber.text;
      data['type'] = 'provider';
      // data['skills'] =
      //     choosedSkills.map((element) => element.id).toList().join(',');
      for (var i = 0; i < choosedSkills.length; i++) {
        data['skills[$i]'] = choosedSkills[i].id;
      }
      // data['work_zones'] =
      //     choosedWorkZones.map((element) => element.id).toList().join(',');
      for (var i = 0; i < choosedWorkZones.length; i++) {
        data['work_zones[$i]'] = choosedWorkZones[i].id;
      }
      data['suspended'] = isSuspend.value ? '1' : '0';
    }

    if (choosedUserType.value == UserType.company) {
      Get.find<HomeController>().changeUserType('provider');
      // provider specific data
      data['provider_name'] = companyName.text;
      data['provider_city_id'] = choosedArea.value.id;
      data['provider_district_id'] = choosedCity.value.id;
      data['provider_phone'] = companyPhone.text.startsWith('0')
          ? companyPhone.text.replaceFirst('0', '+972')
          : companyPhone.text;

      if (companyImage.value.path.isNotEmpty) {
        data['provider_id_file'] = await d.MultipartFile.fromFile(
          companyImage.value.path,
          filename: 'providerImage.png',
        );
      }

      data['provider_id_number'] = companyIdNumber.text;

      data['type'] = 'provider';
      data['skills[]'] =
          choosedSkills.map((element) => element.id).toList().join(',');
      data['work_zones'] =
          choosedWorkZones.map((element) => element.id).toList().join(',');
      data['suspended'] = isSuspend.value ? '1' : '0';
    }

    // user profile image
    // if (userProfile.value.path.isNotEmpty) {
    //   data['image'] = await d.MultipartFile.fromFile(
    //     userProfile.value.path,
    //     filename: 'userProfile.png',
    //   );
    // }
    final user = await myProfileRemoteProvider.editProfileData(
      data,
      userImage: userProfile.value.path,
    );

    if (user != null) {
      homeController.updateUser(user);
    }
  }

  void deleteProfile() async {
    Future<void> logout() async {
      Get.find<GlobalValuesController>().setUserLoggedOut();
      await LocalLoginProvider().deleteUserData();
      Get.offAllNamed(Routes.LOGIN);
    }

    Get.dialog(
      AlertDialog(
        title: Text(
          Get.find<LanguageController>().keys.value.deleteProfile!,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          Get.find<LanguageController>().keys.value.deleteProfileMessage!,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text(
              Get.find<LanguageController>().keys.value.cancel!,
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              showWaitingIndicator();
              try {
                await myProfileRemoteProvider.deleteProfile(
                  user: homeController.user.value,
                );
                await logout();
                hideWaitingIndicator();
              } catch (e) {
                log('deleteProfile error: $e');
                hideWaitingIndicator();
              }
            },
            child: Text(
              Get.find<LanguageController>().keys.value.delete!,
              style: const TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Work Location Methods

  // Fetch work locations from API
  Future<void> fetchWorkLocations() async {
    isLoadingWorkLocations.value = true;
    update();

    try {
      final response = await myProfileRemoteProvider.getWorkLocations();
      if (response != null) {
        workLocations.value = response.data;
      }
    } catch (e) {
      log('Error fetching work locations: ${e.toString()}');
    } finally {
      isLoadingWorkLocations.value = false;
      update();
    }
  }

  // Add new work location
  Future<bool> addWorkLocation() async {
    if (workLocationNameController.text.isEmpty) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseEnterLocationName ??
              'Please enter a location name');
      return false;
    }

    if (workLocationMarker.isEmpty) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseSelectLocation ??
              'Please select a location on the map');
      return false;
    }

    final marker = workLocationMarker.first;
    final location = WorkLocation(
      name: workLocationNameController.text,
      latitude: marker.position.latitude.toString(),
      longitude: marker.position.longitude.toString(),
    );

    final result = await myProfileRemoteProvider.addWorkLocation(location);
    if (result) {
      workLocationNameController.clear();
      workLocationMarker.clear();
      await fetchWorkLocations();
    }

    return result;
  }

  // Edit work location
  Future<bool> editWorkLocation() async {
    if (selectedWorkLocation.value == null) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseSelectLocation ??
              'Please select a location to edit');
      return false;
    }

    if (workLocationMarker.isEmpty) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseSelectLocation ??
              'Please select a location on the map');
      return false;
    }

    if (workLocationNameController.text.isEmpty) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseEnterLocationName ??
              'Please enter a location name');
      return false;
    }

    final marker = workLocationMarker.first;
    final location = WorkLocation(
      id: selectedWorkLocation.value!.id,
      name: workLocationNameController.text,
      latitude: marker.position.latitude.toString(),
      longitude: marker.position.longitude.toString(),
    );

    final result = await myProfileRemoteProvider.editWorkLocation(location);
    if (result) {
      workLocationNameController.clear();
      workLocationMarker.clear();
      selectedWorkLocation.value = null;
      await fetchWorkLocations();
    }

    return result;
  }

  // Delete work location
  Future<bool> deleteWorkLocation(int id) async {
    final result = await myProfileRemoteProvider.deleteWorkLocation(id);
    if (result) {
      await fetchWorkLocations();
    }
    return result;
  }

  // Set marker from map picker
  void setWorkLocationMarker(Set<Marker> marker) {
    workLocationMarker.value = marker;
    update();
  }

  // Select work location for editing
  void selectWorkLocation(WorkLocation location) {
    selectedWorkLocation.value = location;
    workLocationNameController.text = location.name;

    // Set marker for the selected location
    final latitude = double.tryParse(location.latitude) ?? 0.0;
    final longitude = double.tryParse(location.longitude) ?? 0.0;

    workLocationMarker.clear();
    workLocationMarker.add(
      Marker(
        markerId: const MarkerId('selected_work_location'),
        position: LatLng(latitude, longitude),
        icon: BitmapDescriptor.defaultMarker,
      ),
    );

    update();
  }

  // Clear work location selection
  void clearWorkLocationSelection() {
    selectedWorkLocation.value = null;
    workLocationNameController.clear();
    workLocationMarker.clear();
    update();
  }
}
