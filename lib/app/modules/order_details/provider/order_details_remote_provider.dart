import 'dart:developer';

import 'package:get_clean/app/modules/order_details/controllers/state/order_details_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user_booking.dart';

class OrderDetailsRemoteProvider {
  DioHelper helper = DioHelper();

  Future<OrderDetailsState> cancelOrderAsUser(int orderID,
      {bool isOrderChange = false}) async {
    try {
      final response = await helper.postData(
        (isOrderChange ? cancelBookingChangeURL : cancelBookingURL) +
            orderID.toString(),
        {"": null},
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  // approve order as user

  Future<OrderDetailsState> approveOrderAsUser(int orderID) async {
    try {
      final response = await helper.postData(
        approveBookingChangeURL + orderID.toString(),
        {"": null},
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> cancelOrderAsProvider(int orderID) async {
    try {
      final response = await helper.postData(
        cancelOrderByProviderURL + orderID.toString(),
        {"": null},
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> approveOrderAsProvider(int orderID) async {
    try {
      final response = await helper.postData(
        approveOrderByProviderURL + orderID.toString(),
        {"": null},
      );

      if (response != null && response['success'] == false) {
        return OrderDetailsFailed(response['message']);
      } else {
        return OrderDetailsSuccess();
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  //? Approve cloth order
  Future<OrderDetailsState> approveClothOrderAsProvider(int orderID) async {
    try {
      final response = await helper.postData(
        approveClothOrderByProviderURL + orderID.toString(),
        {"": null},
      );

      log('asfDDDD ${response}');

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  //clothFinishedURL
  Future<OrderDetailsState> clothFinished(int orderID) async {
    try {
      final response = await helper.postData(
        clothFinishedURL + orderID.toString(),
        {"": null},
      );

      log('asfDDDD ${response}');

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  //? confirm cloth order
  Future<OrderDetailsState> confirmClothOrderAsProvider(int orderID) async {
    try {
      final response = await helper.postData(
        confirmClothOrderByProviderURL + orderID.toString(),
        {"": null},
      );

      log('asfDDDD ${response}');

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  //? edit cloth order
  Future<OrderDetailsState> editClothOrderAsProvider(
    BookingData bookingData, {
    required Map<int, int> howManyCloth,
  }) async {
    try {
      final orderID = bookingData.id!.toInt();

      final response = await helper.postData(
          editClothOrderByProviderURL + orderID.toString(),
          // data['pricing_option'] = howManyCloth.values.join(',');
          // data['pricing_types'] = howManyCloth.keys.join(',');
          {
            "provider_id": bookingData.provider?.id,
            "service_id": bookingData.orderData?.bookingData?.id,
            "pricing_option": howManyCloth.values.join(','),
            "pricing_types": howManyCloth.keys.join(','),
            "date[]": bookingData.date,
            "time[]": bookingData.startTime,
            "duration[]": bookingData.duration,
            "frequency": bookingData.frequency,
          });

      log('URLL: ${editClothOrderByProviderURL + orderID.toString()} Dataa $howManyCloth asfDDDD ${response}');

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

//? confirm offer as provider
  Future<OrderDetailsState> confirmOfferAsProvider(int orderID,
      //? eg. 2024-04-28 23:53:18
      {required String bookedInDate}) async {
    try {
      final response = await helper.postData(
        confirmProviderServiceOffersURL,
        {
          "order_id": orderID,
          "booked_in": bookedInDate,
        },
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

//? approve offer as provider
  Future<OrderDetailsState> approveOfferAsProvider(
    int orderID, {
    required String tax,
    required String commission,
    required String totalPrice,
    required String commissionPercentage,
    required String startDate,
    required String endDate,
  }) async {
    try {
      final response = await helper.postData(
        approveProviderServiceOffersURL,
        {
          "order_id": orderID,
          "tax": tax,
          "commission": commission,
          "total_price": totalPrice,
          "commission_percentage": commissionPercentage,
          "start_date": '$startDate 00:00:00',
          "end_date": '$endDate 23:59:59',
          "discount_amount": '0',
          "discount_percentage": '0',
        },
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> markAsCompeletedByProvider(int orderID) async {
    try {
      final response = await helper.postData(
        compeleteOrderByProviderURL + orderID.toString(),
        {"": null},
      );

      if (response['success'] == true) {
        return OrderDetailsSuccess();
      } else {
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> requestExtraTime(int orderID, double time) async {
    try {
      final response = await helper.postData(
        submitNewOfferURL + orderID.toString(),
        {"pricing_option": time},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return OrderDetailsSuccess();
      } else {
        showErrorToast(response['message']);
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> updateTrackingStatus(
      int orderID, int stepId) async {
    try {
      final response = await helper.postData(
        updateTrackingStatusURL + orderID.toString(),
        {"step_id": stepId},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return OrderDetailsSuccess();
      } else {
        showErrorToast(response['message']);
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> acceptExtraTime(int id) async {
    try {
      final response = await helper.postData(
        acceptExtraTimeURL + id.toString(),
        {"": null},
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        log(response['data']);
        return OrderDetailsSuccess(
          bookingData: BookingData.fromJson(response['data']),
        );
      } else {
        showErrorToast(response['message']);
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> rejectExtraTime(int id) async {
    try {
      final response = await helper.postData(
        rejectExtraTimeURL + id.toString(),
        {"": null},
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        log(response['data']);
        return OrderDetailsSuccess(
          bookingData: BookingData.fromJson(response['data']),
        );
      } else {
        showErrorToast(response['message']);
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }

  Future<OrderDetailsState> requestInvoice(int orderID) async {
    try {
      final response = await helper.postData(
        requestInvoiceURL + orderID.toString(),
        {"": null},
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        log(response['data']['invoice']);
        return OrderDetailsSuccess();
      } else {
        showErrorToast(response['message']);
        return OrderDetailsFailed(response['message']);
      }
    } catch (e) {
      return OrderDetailsFailed(e.toString());
    }
  }
}
