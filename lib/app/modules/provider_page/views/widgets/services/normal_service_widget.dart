import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/widget/text_with_background.dart';

class NormalServiceWidget extends StatelessWidget {
  final ProviderPageController controller;
  const NormalServiceWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          child: Text(
            Get.find<LanguageController>().keys.value.workingTimes!,
            style: big2TextStyle,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(10),
          margin: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: const Color(0xffF3F3F3),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                      color: primaryColor,
                      text: Get.find<LanguageController>().keys.value.days!,
                    ),
                    for (int i = 0;
                        i < controller.provider.workingTime!.length;
                        i++)
                      Obx(() => GestureDetector(
                            onTap: () => controller.onChangeSelectedDay(
                                controller.provider.workingTime![i].day ?? ''),
                            child: TextWithBackground(
                              color: controller.selectedDay.value ==
                                      controller.provider.workingTime![i].day
                                  ? primaryColor
                                  : Colors.white,
                              text: controller.provider.workingTime![i].day
                                      ?.removeBrackets() ??
                                  '',
                              textColor: controller.selectedDay.value ==
                                      controller.provider.workingTime![i].day
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          )),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                      color: primaryColor,
                      text: Get.find<LanguageController>().keys.value.open!,
                    ),
                    for (int i = 0;
                        i < controller.provider.workingTime!.length;
                        i++)
                      TextWithBackground(
                        color: Colors.white,
                        text: controller.provider.workingTime![i].startsAt
                                ?.removeBrackets() ??
                            '',
                        textColor: Colors.black,
                      ),
                  ],
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                      color: primaryColor,
                      text: Get.find<LanguageController>().keys.value.close!,
                    ),
                    for (int i = 0;
                        i < controller.provider.workingTime!.length;
                        i++)
                      TextWithBackground(
                        color: Colors.white,
                        text: controller.provider.workingTime![i].endsAt
                                ?.removeBrackets() ??
                            '',
                        textColor: Colors.black,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(10),
          child: Text(
            Get.find<LanguageController>().keys.value.holidays!,
            style: big2TextStyle,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(10),
          margin: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: const Color(0xffF3F3F3),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                      color: primaryColor,
                      text: Get.find<LanguageController>().keys.value.from!,
                    ),
                    for (int i = 0;
                        i < controller.provider.holidays!.length;
                        i++)
                      TextWithBackground(
                        color: Colors.white,
                        text: controller.provider.holidays![i].startsAt
                                ?.removeBrackets() ??
                            '',
                        textColor: Colors.black,
                      ),
                  ],
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                      color: primaryColor,
                      text: Get.find<LanguageController>().keys.value.to!,
                    ),
                    for (int i = 0;
                        i < controller.provider.holidays!.length;
                        i++)
                      TextWithBackground(
                        color: Colors.white,
                        text: controller.provider.holidays![i].endsAt
                                ?.removeBrackets() ??
                            '',
                        textColor: Colors.black,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
